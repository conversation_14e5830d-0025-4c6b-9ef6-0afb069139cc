import 'package:towasl/core/helpers/custom_countries.dart';
import 'package:url_launcher/url_launcher.dart';

/// Application Constants
///
/// Contains all the constant values used throughout the application
/// including default settings, URLs, and API keys
class Constants {
  /// Default country settings for Saudi Arabia
  static const String defaultCountryDialCode = '966';
  static const String defaultCountryCode = 'SA';

  /// Default country data for phone number input
  static Country defaultCountryData = const Country(
    name: "Saudi Arabia",
    flag: "🇸🇦",
    code: "SA",
    dialCode: "966",
    minLength: 9,
    maxLength: 9,
    nameTranslations: {},
  );

  /// Custom country data for specialized inputs
  static Country defaultCustomCountryData = const Country(
    name: "Saudi Arabia",
    flag: "🇸🇦",
    code: "SA",
    dialCode: "966",
    minLength: 9,
    maxLength: 9,
    nameTranslations: {},
  );

  /// Helper method to launch URLs
  ///
  /// Opens the provided URL in the device's default browser
  /// Throws an exception if the URL cannot be launched
  ///
  /// @param url The URL to open
  static launchURL(String url) async {
    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      throw 'Could not launch $url';
    }
  }

  /// Legal URLs
  static const termsUrl = "https://towasl.com/terms-conditions";
  static const privacyUrl = "https://towasl.com/privacy-policy";

  /// OneSignal push notification configuration
  static const oneSignalAppID = "************************************";
  static const oneSignalSendNotiUrl =
      "https://onesignal.com/api/v1/notifications";

  /// Firebase and messaging configuration
  static const firebaseServerKey =
      "AAAAzTCKko4:APA91bF3zHHuW0xImdZPC_mkykzxdhd3auFG4wfKoAolHd7c8pNeI5bgIxN5cpW6zqPCFHlrvTTOUp2gaVFyOHPMxjEllSNuDG1A7QOEZLzcqNb9FxyMVBkoT6MYsgLk78gub2o_4ie2";
  static const sendNotiUrl = "https://fcm.googleapis.com/fcm/send";
  static const msegatSendFreeSmsUrl = "https://www.msegat.com/gw/sendsms.php";

  /// Validation constants
  static const yearLength = 4;
}
