/// Storage Service
///
/// Provides methods for storing and retrieving persistent data
/// Used for saving user preferences, login state, and other app settings
///
/// This utility uses GetStorage to store data locally on the device
library storage_service;

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/models/user_model.dart';

/// Storage keys used throughout the application
class StorageKeys {
  static const String loggedIn = 'loggedIn';
  static const String userID = 'userID';
  static const String langCode = 'lang_code';
  static const String isFirstTime = 'isFirstTime';
  static const String termsAccepted = 'termsAccepted';
}

/// Storage Service Class
///
/// Contains static methods for managing persistent data
/// Handles user login state, language preferences, and other settings
class StorageService {
  /// The GetStorage instance used for all storage operations
  static final GetStorage _storage = GetStorage();

  /// Initializes the storage service
  ///
  /// Must be called before using any storage methods
  static Future<void> init() async {
    await GetStorage.init();
  }

  /// Saves user login data to storage
  ///
  /// Sets the logged-in status to true and saves the user ID
  ///
  /// @param userID The ID of the logged-in user
  static void setLoginData(String userID) {
    _storage.write(StorageKeys.loggedIn, true);
    _storage.write(StorageKeys.userID, userID);
  }

  /// Retrieves the user's logged-in status
  ///
  /// @return True if the user is logged in, false otherwise
  static bool getLoggedInStatus() {
    return _storage.read(StorageKeys.loggedIn) ?? false;
  }

  /// Retrieves the user ID and sets the global userID variable
  ///
  /// Gets the user ID from storage and updates the global variable
  /// Logs the user ID in debug mode
  static void getUserID() {
    final id = getUserIDValue();
    if (kDebugMode) {
      print('userID:$id');
    }
  }

  /// Retrieves the user ID value from storage
  ///
  /// @return The stored user ID, or an empty string if none is stored
  static String getUserIDValue() {
    return _storage.read(StorageKeys.userID) ?? '';
  }

  /// Saves the selected language code to storage
  ///
  /// @param langCode The language code to save (e.g., "en", "ar")
  static void setLanguage(String langCode) {
    _storage.write(StorageKeys.langCode, langCode);
  }

  /// Retrieves the saved language code
  ///
  /// @return The saved language code, or "en" (English) if none is saved
  static String getLanguage() {
    return _storage.read(StorageKeys.langCode) ?? "en";
  }

  /// Clears user data from storage and resets global variables
  ///
  /// Used during logout or account deletion
  /// Resets both storage and in-memory state
  /// Preserves terms acceptance state
  ///
  /// @return A Future that completes when all data has been cleared
  static Future<void> clearUserData() async {
    // Save the terms acceptance state before clearing
    bool termsAccepted = getTermsAccepted();
    if (kDebugMode) {
      print('Preserving terms acceptance state during logout: $termsAccepted');
    }

    // Clear all storage
    await _storage.erase();

    // Restore the terms acceptance state
    await setTermsAccepted(termsAccepted);

    // Reset AppController state if it exists
    if (Get.isRegistered<AppController>()) {
      AppController appController = Get.find();
      appController.clearUserData();
    }

    // Reset UserController state if it exists
    if (Get.isRegistered<UserController>()) {
      UserController userController = Get.find();
      userController.userModel.value = UserModel();
      userController.userInterestList.clear();

      // Clear form controllers
      userController.nameController.clear();
      userController.nationalityController.clear();
      userController.yearController.clear();
      userController.locationController.clear();
      userController.emailController.clear();

      // Reset gender and validation state
      userController.gender = null;
      userController.showGenderValidation.value = false;
      userController.showValidationError.value = false;

      if (kDebugMode) {
        print("StorageService: UserController reset, gender validation state: ${userController.showGenderValidation.value}");
      }
    }
  }

  /// Sets the first-time app launch flag to false
  ///
  /// Used to track whether this is the first time the app has been launched
  /// Typically called after onboarding or initial setup
  static void setFirstTime() {
    _storage.write(StorageKeys.isFirstTime, false);
  }

  /// Checks if this is the first time the app has been launched
  ///
  /// @return True if this is the first launch, false otherwise
  static bool getFirstTime() {
    return _storage.read(StorageKeys.isFirstTime) ?? true;
  }

  /// Saves the terms and conditions acceptance state
  ///
  /// @param accepted Whether the user has accepted the terms and conditions
  static Future<void> setTermsAccepted(bool accepted) async {
    // Write the value to storage
    await _storage.write(StorageKeys.termsAccepted, accepted);

    // Force a save to disk to ensure persistence
    await _storage.save();

    if (kDebugMode) {
      print('StorageService: Saved terms acceptance state: $accepted');
      print('StorageService: Verification read: ${_storage.read(StorageKeys.termsAccepted)}');
    }
  }

  /// Retrieves the terms and conditions acceptance state
  ///
  /// @return True if the user has accepted the terms, false otherwise
  static bool getTermsAccepted() {
    bool result = _storage.read(StorageKeys.termsAccepted) ?? false;
    if (kDebugMode) {
      print('StorageService: Retrieved terms acceptance state: $result');
    }
    return result;
  }
}
