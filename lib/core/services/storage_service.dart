/// Storage Service
///
/// Provides methods for storing and retrieving persistent data
/// Used for saving user preferences, login state, and other app settings
///
/// This service uses GetStorage to store data locally on the device
library storage_service;

import 'package:flutter/foundation.dart';
import 'package:get_storage/get_storage.dart';

/// Storage keys used throughout the application
class StorageKeys {
  static const String loggedIn = 'loggedIn';
  static const String userID = 'userID';
  static const String langCode = 'lang_code';
  static const String isFirstTime = 'isFirstTime';
  static const String termsAccepted = 'termsAccepted';
}

/// Storage Service Interface
///
/// Defines the contract for storage operations
/// Allows for easy mocking in tests
abstract class StorageService {
  /// Saves user login data to storage
  ///
  /// Sets the logged-in status to true and saves the user ID
  ///
  /// @param userID The ID of the logged-in user
  void setLoginData(String userID);

  /// Retrieves the user's logged-in status
  ///
  /// @return True if the user is logged in, false otherwise
  bool getLoggedInStatus();

  /// Retrieves the user ID value from storage
  ///
  /// @return The stored user ID, or an empty string if none is stored
  String getUserIDValue();

  /// Saves the selected language code to storage
  ///
  /// @param langCode The language code to save (e.g., "en", "ar")
  void setLanguage(String langCode);

  /// Retrieves the saved language code
  ///
  /// @return The saved language code, or "en" (English) if none is saved
  String getLanguage();

  /// Clears user data from storage
  ///
  /// Used during logout or account deletion
  /// Preserves terms acceptance state
  ///
  /// @return A Future that completes when all data has been cleared
  Future<void> clearUserData();

  /// Sets the first-time app launch flag to false
  ///
  /// Used to track whether this is the first time the app has been launched
  /// Typically called after onboarding or initial setup
  void setFirstTime();

  /// Checks if this is the first time the app has been launched
  ///
  /// @return True if this is the first launch, false otherwise
  bool getFirstTime();

  /// Saves the terms and conditions acceptance state
  ///
  /// @param accepted Whether the user has accepted the terms and conditions
  Future<void> setTermsAccepted(bool accepted);

  /// Retrieves the terms and conditions acceptance state
  ///
  /// @return True if the user has accepted the terms, false otherwise
  bool getTermsAccepted();
}

/// Storage Service Implementation
///
/// Implements the StorageService interface using GetStorage
class StorageServiceImpl implements StorageService {
  /// The GetStorage instance used for all storage operations
  final GetStorage _storage;

  /// Constructor that takes a GetStorage instance
  ///
  /// @param storage The GetStorage instance to use
  StorageServiceImpl(this._storage);

  @override
  void setLoginData(String userID) {
    _storage.write(StorageKeys.loggedIn, true);
    _storage.write(StorageKeys.userID, userID);
  }

  @override
  bool getLoggedInStatus() {
    return _storage.read(StorageKeys.loggedIn) ?? false;
  }

  @override
  String getUserIDValue() {
    return _storage.read(StorageKeys.userID) ?? '';
  }

  @override
  void setLanguage(String langCode) {
    _storage.write(StorageKeys.langCode, langCode);
  }

  @override
  String getLanguage() {
    return _storage.read(StorageKeys.langCode) ?? "en";
  }

  @override
  Future<void> clearUserData() async {
    // Save the terms acceptance state before clearing
    bool termsAccepted = getTermsAccepted();
    if (kDebugMode) {
      print('Preserving terms acceptance state during logout: $termsAccepted');
    }

    // Clear all storage
    await _storage.erase();

    // Restore the terms acceptance state
    await setTermsAccepted(termsAccepted);
  }

  @override
  void setFirstTime() {
    _storage.write(StorageKeys.isFirstTime, false);
  }

  @override
  bool getFirstTime() {
    return _storage.read(StorageKeys.isFirstTime) ?? true;
  }

  @override
  Future<void> setTermsAccepted(bool accepted) async {
    // Write the value to storage
    await _storage.write(StorageKeys.termsAccepted, accepted);

    // Force a save to disk to ensure persistence
    await _storage.save();

    if (kDebugMode) {
      print('StorageService: Saved terms acceptance state: $accepted');
      print('StorageService: Verification read: ${_storage.read(StorageKeys.termsAccepted)}');
    }
  }

  @override
  bool getTermsAccepted() {
    bool result = _storage.read(StorageKeys.termsAccepted) ?? false;
    if (kDebugMode) {
      print('StorageService: Retrieved terms acceptance state: $result');
    }
    return result;
  }
}
