import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:towasl/core/services/navigation_service.dart';

/// Navigation Service Implementation
/// 
/// Concrete implementation of NavigationService using GetX
/// Provides centralized navigation with logging, analytics, and error handling
class NavigationServiceImpl implements NavigationService {
  // Internal state
  final List<String> _navigationHistory = [];
  final List<Function(String route, dynamic arguments)> _listeners = [];

  /// Log navigation events for debugging and analytics
  void _logNavigation(String action, String destination, {dynamic arguments}) {
    if (kDebugMode) {
      print('Navigation: $action -> $destination');
      if (arguments != null) {
        print('Arguments: $arguments');
      }
    }
    
    // Add to history
    _navigationHistory.add('$action:$destination');
    
    // Notify listeners
    for (final listener in _listeners) {
      try {
        listener(destination, arguments);
      } catch (e) {
        if (kDebugMode) {
          print('Error in navigation listener: $e');
        }
      }
    }
  }

  @override
  Future<T?> to<T>(
    Widget page, {
    Bindings? binding,
    Transition? transition,
    dynamic arguments,
  }) async {
    try {
      final pageName = page.runtimeType.toString();
      _logNavigation('to', pageName, arguments: arguments);
      
      return await Get.to<T>(
        () => page,
        binding: binding,
        transition: transition,
        arguments: arguments,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.to: $e');
      }
      rethrow;
    }
  }

  @override
  Future<T?> toNamed<T>(
    String routeName, {
    dynamic arguments,
  }) async {
    try {
      _logNavigation('toNamed', routeName, arguments: arguments);
      
      return await Get.toNamed<T>(
        routeName,
        arguments: arguments,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.toNamed: $e');
      }
      rethrow;
    }
  }

  @override
  Future<T?> off<T>(
    Widget page, {
    Bindings? binding,
    Transition? transition,
    dynamic arguments,
  }) async {
    try {
      final pageName = page.runtimeType.toString();
      _logNavigation('off', pageName, arguments: arguments);
      
      return await Get.off<T>(
        () => page,
        binding: binding,
        transition: transition,
        arguments: arguments,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.off: $e');
      }
      rethrow;
    }
  }

  @override
  Future<T?> offNamed<T>(
    String routeName, {
    dynamic arguments,
  }) async {
    try {
      _logNavigation('offNamed', routeName, arguments: arguments);
      
      return await Get.offNamed<T>(
        routeName,
        arguments: arguments,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.offNamed: $e');
      }
      rethrow;
    }
  }

  @override
  Future<T?> offAll<T>(
    Widget page, {
    Bindings? binding,
    Transition? transition,
    dynamic arguments,
  }) async {
    try {
      final pageName = page.runtimeType.toString();
      _logNavigation('offAll', pageName, arguments: arguments);
      
      return await Get.offAll<T>(
        () => page,
        binding: binding,
        transition: transition,
        arguments: arguments,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.offAll: $e');
      }
      rethrow;
    }
  }

  @override
  Future<T?> offAllNamed<T>(
    String routeName, {
    dynamic arguments,
  }) async {
    try {
      _logNavigation('offAllNamed', routeName, arguments: arguments);
      
      return await Get.offAllNamed<T>(
        routeName,
        arguments: arguments,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.offAllNamed: $e');
      }
      rethrow;
    }
  }

  @override
  void back<T>({T? result}) {
    try {
      _logNavigation('back', 'previous_page');
      Get.back<T>(result: result);
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.back: $e');
      }
      rethrow;
    }
  }

  @override
  void until(bool Function(Route<dynamic>) predicate) {
    try {
      _logNavigation('until', 'conditional_back');
      Get.until(predicate);
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.until: $e');
      }
      rethrow;
    }
  }

  @override
  bool canPop() {
    try {
      return Get.key.currentState?.canPop() ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.canPop: $e');
      }
      return false;
    }
  }

  @override
  String? get currentRoute {
    try {
      return Get.currentRoute;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current route: $e');
      }
      return null;
    }
  }

  @override
  List<String> get navigationHistory {
    return List.unmodifiable(_navigationHistory);
  }

  @override
  Future<T?> showDialog<T>(
    Widget dialog, {
    bool barrierDismissible = true,
  }) async {
    try {
      _logNavigation('showDialog', dialog.runtimeType.toString());
      
      return await Get.dialog<T>(
        dialog,
        barrierDismissible: barrierDismissible,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.showDialog: $e');
      }
      rethrow;
    }
  }

  @override
  Future<T?> showBottomSheet<T>(
    Widget bottomSheet, {
    bool isScrollControlled = false,
  }) async {
    try {
      _logNavigation('showBottomSheet', bottomSheet.runtimeType.toString());
      
      return await Get.bottomSheet<T>(
        bottomSheet,
        isScrollControlled: isScrollControlled,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.showBottomSheet: $e');
      }
      rethrow;
    }
  }

  @override
  void showSnackbar(
    String title,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    try {
      _logNavigation('showSnackbar', title);
      
      Get.snackbar(
        title,
        message,
        duration: duration,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in navigation.showSnackbar: $e');
      }
    }
  }

  @override
  void clearHistory() {
    _navigationHistory.clear();
    if (kDebugMode) {
      print('Navigation history cleared');
    }
  }

  @override
  void addNavigationListener(Function(String route, dynamic arguments) listener) {
    _listeners.add(listener);
    if (kDebugMode) {
      print('Navigation listener added. Total listeners: ${_listeners.length}');
    }
  }

  @override
  void removeNavigationListener(Function(String route, dynamic arguments) listener) {
    _listeners.remove(listener);
    if (kDebugMode) {
      print('Navigation listener removed. Total listeners: ${_listeners.length}');
    }
  }
}
