import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:towasl/bindings/home_binding.dart';
import 'package:towasl/bindings/interests_binding.dart';
import 'package:towasl/bindings/location_binding.dart';
import 'package:towasl/bindings/personal_info_binding.dart';
import 'package:towasl/core/services/navigation_service.dart';
import 'package:towasl/views/interests_page.dart';
import 'package:towasl/views/location_page.dart';
import 'package:towasl/views/personal_info_page/personal_info_page.dart';
import 'package:towasl/views/settings_page.dart';

/// Settings Navigation Service
///
/// Handles navigation flows related to settings and profile editing
/// Provides high-level navigation methods for settings-related screens
class SettingsNavigationService {
  final NavigationService _navigationService;

  /// Constructor with dependency injection
  ///
  /// @param navigationService The core navigation service
  SettingsNavigationService({
    required NavigationService navigationService,
  }) : _navigationService = navigationService;

  /// Navigate to settings page
  ///
  /// Opens the settings page from the home screen
  /// Returns a future that completes when the user returns from settings
  Future<T?> navigateToSettings<T>() async {
    try {
      if (kDebugMode) {
        print('Navigating to settings page');
      }

      return await _navigationService.to<T>(
        const SettingsPage(),
        binding: HomeBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to settings: $e');
      }
      rethrow;
    }
  }

  /// Navigate to edit profile page
  ///
  /// Opens the personal information page in editing mode
  /// Used when user wants to edit their profile from settings
  Future<T?> navigateToEditProfile<T>() async {
    try {
      if (kDebugMode) {
        print('Navigating to edit profile page');
      }

      return await _navigationService.to<T>(
        const PersonalInfoPage(isEditing: true),
        binding: PersonalInfoBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to edit profile: $e');
      }
      rethrow;
    }
  }

  /// Navigate to edit interests page
  ///
  /// Opens the interests page in settings mode
  /// Used when user wants to edit their interests from settings
  Future<T?> navigateToEditInterests<T>() async {
    try {
      if (kDebugMode) {
        print('Navigating to edit interests page');
      }

      return await _navigationService.to<T>(
        const InterestsPage(isFromSetting: true),
        binding: InterestsBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to edit interests: $e');
      }
      rethrow;
    }
  }

  /// Navigate to edit location page
  ///
  /// Opens the location page in editing mode
  /// Used when user wants to edit their location from settings
  Future<T?> navigateToEditLocation<T>() async {
    try {
      if (kDebugMode) {
        print('Navigating to edit location page');
      }

      return await _navigationService.to<T>(
        const LocationPage(isFromEdit: true),
        binding: LocationBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to edit location: $e');
      }
      rethrow;
    }
  }

  /// Go back to previous screen
  ///
  /// Returns to the previous screen, typically used after editing
  void goBack<T>({T? result}) {
    try {
      if (kDebugMode) {
        print('Going back from settings screen');
      }

      _navigationService.back<T>(result: result);
    } catch (e) {
      if (kDebugMode) {
        print('Error going back: $e');
      }
      rethrow;
    }
  }

  /// Navigate to settings section
  ///
  /// Generic method to navigate to different settings sections
  ///
  /// @param section The settings section to navigate to
  Future<T?> navigateToSettingsSection<T>(String section) async {
    try {
      switch (section.toLowerCase()) {
        case 'profile':
        case 'edit_profile':
          return await navigateToEditProfile<T>();

        case 'interests':
        case 'edit_interests':
          return await navigateToEditInterests<T>();

        case 'location':
        case 'edit_location':
          return await navigateToEditLocation<T>();

        default:
          if (kDebugMode) {
            print('Unknown settings section: $section');
          }
          return await navigateToSettings<T>();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to settings section: $e');
      }
      rethrow;
    }
  }

  /// Check if can navigate back
  ///
  /// Determines if there's a previous screen to go back to
  ///
  /// @return True if can go back, false otherwise
  bool canGoBack() {
    return _navigationService.canPop();
  }

  /// Show settings confirmation dialog
  ///
  /// Shows a confirmation dialog for settings actions
  ///
  /// @param title The dialog title
  /// @param message The dialog message
  /// @param confirmText The confirmation button text
  /// @param cancelText The cancel button text
  /// @return Future that completes with true if confirmed, false if cancelled
  Future<bool> showConfirmationDialog({
    required String title,
    required String message,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) async {
    try {
      if (kDebugMode) {
        print('Showing settings confirmation dialog: $title');
      }

      // Create a simple confirmation dialog
      final result = await _navigationService.showDialog<bool>(
        AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => _navigationService.back(result: false),
              child: Text(cancelText),
            ),
            TextButton(
              onPressed: () => _navigationService.back(result: true),
              child: Text(confirmText),
            ),
          ],
        ),
      );

      return result ?? false;
    } catch (e) {
      if (kDebugMode) {
        print('Error showing confirmation dialog: $e');
      }
      return false;
    }
  }

  /// Show settings success message
  ///
  /// Shows a success snackbar for settings operations
  ///
  /// @param message The success message to display
  void showSuccessMessage(String message) {
    try {
      if (kDebugMode) {
        print('Showing settings success message: $message');
      }

      _navigationService.showSnackbar(
        'Success',
        message,
        duration: const Duration(seconds: 2),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error showing success message: $e');
      }
    }
  }

  /// Show settings error message
  ///
  /// Shows an error snackbar for settings operations
  ///
  /// @param message The error message to display
  void showErrorMessage(String message) {
    try {
      if (kDebugMode) {
        print('Showing settings error message: $message');
      }

      _navigationService.showSnackbar(
        'Error',
        message,
        duration: const Duration(seconds: 3),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error showing error message: $e');
      }
    }
  }
}
