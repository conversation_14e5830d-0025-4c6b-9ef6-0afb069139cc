import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/home_binding.dart';
import 'package:towasl/bindings/interests_binding.dart';
import 'package:towasl/bindings/location_binding.dart';
import 'package:towasl/bindings/mobile_otp_binding.dart';
import 'package:towasl/bindings/personal_info_binding.dart';
import 'package:towasl/bindings/signup_login_binding.dart';
import 'package:towasl/core/services/navigation_service.dart';
import 'package:towasl/models/user_model.dart';
import 'package:towasl/views/home_page/home_page.dart';
import 'package:towasl/views/interests_page.dart';
import 'package:towasl/views/location_page.dart';
import 'package:towasl/views/mobile_otp_page/mobile_otp_page.dart';
import 'package:towasl/views/personal_info_page/personal_info_page.dart';
import 'package:towasl/views/signup_login_page/signup_login_page.dart';

/// Authentication Navigation Service
///
/// Handles navigation flows related to authentication and user onboarding
/// Provides high-level navigation methods for auth-related screens
class AuthNavigationService {
  final NavigationService _navigationService;

  /// Constructor with dependency injection
  ///
  /// @param navigationService The core navigation service
  AuthNavigationService({
    required NavigationService navigationService,
  }) : _navigationService = navigationService;

  /// Navigate to OTP verification page
  ///
  /// Used when a new user needs to verify their mobile number
  ///
  /// @param mobileNumber The mobile number to verify
  /// @param userId The generated user ID
  Future<void> navigateToOtp({
    required String mobileNumber,
    required String userId,
  }) async {
    try {
      if (kDebugMode) {
        print('Navigating to OTP verification for mobile: $mobileNumber');
      }

      await _navigationService.to(
        const MobileOtpPage(),
        binding: MobileOtpBinding(
          mobileNumber: mobileNumber,
          userId: userId,
        ),
        transition: Transition.rightToLeft,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to OTP: $e');
      }
      rethrow;
    }
  }

  /// Navigate to login page
  ///
  /// Used for logout or when authentication is required
  Future<void> navigateToLogin() async {
    try {
      if (kDebugMode) {
        print('Navigating to login page');
      }

      await _navigationService.offAll(
        const SignupLoginPage(),
        binding: SignupLoginBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to login: $e');
      }
      rethrow;
    }
  }

  /// Navigate based on user profile completion status
  ///
  /// Determines the appropriate screen based on what profile information
  /// the user has completed and navigates accordingly
  ///
  /// @param user The user model to check for completion status
  Future<void> navigateBasedOnProfile(UserModel user) async {
    try {
      if (kDebugMode) {
        print('Navigating based on profile completion');
        print('User interests: ${user.userInterest != null}');
        print('User location: ${user.userLocation != null}');
        print('User nationality: ${(user.nationality ?? "").isNotEmpty}');
        print('User name: ${(user.name ?? "").isNotEmpty}');
      }

      // Check profile completion in order of onboarding flow
      if (user.userInterest == null || (user.userInterest?.isEmpty ?? true)) {
        // User needs to select interests
        await navigateToInterests();
      } else if (user.userLocation == null) {
        // User needs to set location
        await navigateToLocation();
      } else if ((user.nationality ?? "").isEmpty ||
          (user.name ?? "").isEmpty ||
          (user.gender ?? "").isEmpty ||
          (user.birthdayYear ?? "").isEmpty) {
        // User needs to complete personal information
        await navigateToPersonalInfo();
      } else {
        // User profile is complete, go to home page
        await navigateToHome();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in profile-based navigation: $e');
      }
      rethrow;
    }
  }

  /// Navigate to interests selection page
  ///
  /// Used during onboarding when user needs to select interests
  Future<void> navigateToInterests() async {
    try {
      if (kDebugMode) {
        print('Navigating to interests page');
      }

      await _navigationService.offAll(
        const InterestsPage(),
        binding: InterestsBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to interests: $e');
      }
      rethrow;
    }
  }

  /// Navigate to location setup page
  ///
  /// Used during onboarding when user needs to set their location
  Future<void> navigateToLocation() async {
    try {
      if (kDebugMode) {
        print('Navigating to location page');
      }

      await _navigationService.offAll(
        const LocationPage(isFromEdit: false),
        binding: LocationBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to location: $e');
      }
      rethrow;
    }
  }

  /// Navigate to personal information page
  ///
  /// Used during onboarding when user needs to complete personal info
  Future<void> navigateToPersonalInfo() async {
    try {
      if (kDebugMode) {
        print('Navigating to personal info page');
      }

      await _navigationService.offAll(
        const PersonalInfoPage(),
        binding: PersonalInfoBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to personal info: $e');
      }
      rethrow;
    }
  }

  /// Navigate to home page
  ///
  /// Used when user profile is complete and they can access the main app
  Future<void> navigateToHome() async {
    try {
      if (kDebugMode) {
        print('Navigating to home page');
      }

      await _navigationService.offAll(
        const HomePage(),
        binding: HomeBinding(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to home: $e');
      }
      rethrow;
    }
  }

  /// Navigate to next step in onboarding flow
  ///
  /// Automatically determines and navigates to the next step in the onboarding process
  ///
  /// @param currentStep The current onboarding step
  Future<void> navigateToNextOnboardingStep(String currentStep) async {
    try {
      switch (currentStep) {
        case 'interests':
          await navigateToLocation();
          break;
        case 'location':
          await navigateToPersonalInfo();
          break;
        case 'personalInfo':
          await navigateToHome();
          break;
        default:
          if (kDebugMode) {
            print('Unknown onboarding step: $currentStep');
          }
          await navigateToInterests(); // Default to first step
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error navigating to next onboarding step: $e');
      }
      rethrow;
    }
  }

  /// Check if user needs onboarding
  ///
  /// Determines if the user needs to go through the onboarding flow
  ///
  /// @param user The user model to check
  /// @return True if user needs onboarding, false if profile is complete
  bool needsOnboarding(UserModel user) {
    return user.userInterest == null ||
        user.userLocation == null ||
        (user.nationality ?? "").isEmpty ||
        (user.name ?? "").isEmpty ||
        (user.gender ?? "").isEmpty ||
        (user.birthdayYear ?? "").isEmpty;
  }

  /// Get onboarding completion percentage
  ///
  /// Calculates how much of the onboarding process the user has completed
  ///
  /// @param user The user model to check
  /// @return Completion percentage (0.0 to 1.0)
  double getOnboardingProgress(UserModel user) {
    int completedSteps = 0;
    const int totalSteps = 4;

    if (user.userInterest != null && (user.userInterest?.isNotEmpty ?? false)) {
      completedSteps++;
    }
    if (user.userLocation != null) {
      completedSteps++;
    }
    if ((user.nationality ?? "").isNotEmpty) {
      completedSteps++;
    }
    if ((user.name ?? "").isNotEmpty &&
        (user.gender ?? "").isNotEmpty &&
        (user.birthdayYear ?? "").isNotEmpty) {
      completedSteps++;
    }

    return completedSteps / totalSteps;
  }
}
