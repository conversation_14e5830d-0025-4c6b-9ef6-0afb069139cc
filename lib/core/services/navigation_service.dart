import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Navigation Service Interface
/// 
/// Provides a centralized interface for all navigation operations in the app
/// Abstracts GetX navigation methods to improve testability and maintainability
abstract class NavigationService {
  /// Navigate to a new page (push)
  /// 
  /// Pushes a new page onto the navigation stack
  /// 
  /// @param page The widget/page to navigate to
  /// @param binding Optional binding for dependency injection
  /// @param transition Optional transition animation
  /// @param arguments Optional arguments to pass to the page
  /// @return Future that completes with the result from the page
  Future<T?> to<T>(
    Widget page, {
    Bindings? binding,
    Transition? transition,
    dynamic arguments,
  });

  /// Navigate to a new page by name (push)
  /// 
  /// Pushes a new page onto the navigation stack using named routes
  /// 
  /// @param routeName The name of the route to navigate to
  /// @param arguments Optional arguments to pass to the page
  /// @return Future that completes with the result from the page
  Future<T?> toNamed<T>(
    String routeName, {
    dynamic arguments,
  });

  /// Replace current page (off)
  /// 
  /// Replaces the current page with a new one
  /// 
  /// @param page The widget/page to navigate to
  /// @param binding Optional binding for dependency injection
  /// @param transition Optional transition animation
  /// @param arguments Optional arguments to pass to the page
  /// @return Future that completes with the result from the page
  Future<T?> off<T>(
    Widget page, {
    Bindings? binding,
    Transition? transition,
    dynamic arguments,
  });

  /// Replace current page by name (off)
  /// 
  /// Replaces the current page with a new one using named routes
  /// 
  /// @param routeName The name of the route to navigate to
  /// @param arguments Optional arguments to pass to the page
  /// @return Future that completes with the result from the page
  Future<T?> offNamed<T>(
    String routeName, {
    dynamic arguments,
  });

  /// Clear navigation stack and navigate (offAll)
  /// 
  /// Clears the entire navigation stack and navigates to a new page
  /// 
  /// @param page The widget/page to navigate to
  /// @param binding Optional binding for dependency injection
  /// @param transition Optional transition animation
  /// @param arguments Optional arguments to pass to the page
  /// @return Future that completes with the result from the page
  Future<T?> offAll<T>(
    Widget page, {
    Bindings? binding,
    Transition? transition,
    dynamic arguments,
  });

  /// Clear navigation stack and navigate by name (offAll)
  /// 
  /// Clears the entire navigation stack and navigates to a new page using named routes
  /// 
  /// @param routeName The name of the route to navigate to
  /// @param arguments Optional arguments to pass to the page
  /// @return Future that completes with the result from the page
  Future<T?> offAllNamed<T>(
    String routeName, {
    dynamic arguments,
  });

  /// Go back to previous page (pop)
  /// 
  /// Pops the current page from the navigation stack
  /// 
  /// @param result Optional result to return to the previous page
  void back<T>({T? result});

  /// Go back until a specific condition is met
  /// 
  /// Pops pages from the navigation stack until the predicate returns true
  /// 
  /// @param predicate Function that determines when to stop popping
  void until(bool Function(Route<dynamic>) predicate);

  /// Check if navigation can go back
  /// 
  /// Returns true if there are pages in the navigation stack to go back to
  /// 
  /// @return True if can pop, false otherwise
  bool canPop();

  /// Get the current route name
  /// 
  /// Returns the name of the currently active route
  /// 
  /// @return Current route name or null if not available
  String? get currentRoute;

  /// Get navigation history
  /// 
  /// Returns a list of route names representing the navigation history
  /// 
  /// @return List of route names in navigation history
  List<String> get navigationHistory;

  /// Show a dialog
  /// 
  /// Displays a dialog over the current page
  /// 
  /// @param dialog The dialog widget to show
  /// @param barrierDismissible Whether the dialog can be dismissed by tapping outside
  /// @return Future that completes with the result from the dialog
  Future<T?> showDialog<T>(
    Widget dialog, {
    bool barrierDismissible = true,
  });

  /// Show a bottom sheet
  /// 
  /// Displays a bottom sheet over the current page
  /// 
  /// @param bottomSheet The bottom sheet widget to show
  /// @param isScrollControlled Whether the bottom sheet should be scroll controlled
  /// @return Future that completes with the result from the bottom sheet
  Future<T?> showBottomSheet<T>(
    Widget bottomSheet, {
    bool isScrollControlled = false,
  });

  /// Show a snackbar
  /// 
  /// Displays a snackbar with the given message
  /// 
  /// @param title The title of the snackbar
  /// @param message The message to display
  /// @param duration How long to show the snackbar
  void showSnackbar(
    String title,
    String message, {
    Duration duration = const Duration(seconds: 3),
  });

  /// Clear navigation history
  /// 
  /// Clears the internal navigation history tracking
  void clearHistory();

  /// Add navigation listener
  /// 
  /// Adds a listener that will be called on navigation events
  /// 
  /// @param listener Function to call on navigation events
  void addNavigationListener(Function(String route, dynamic arguments) listener);

  /// Remove navigation listener
  /// 
  /// Removes a previously added navigation listener
  /// 
  /// @param listener Function to remove from navigation listeners
  void removeNavigationListener(Function(String route, dynamic arguments) listener);
}
