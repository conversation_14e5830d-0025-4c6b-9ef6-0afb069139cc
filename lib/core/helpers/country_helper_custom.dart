/// Country Helper Functions
///
/// Provides utility functions for working with country data
/// Includes string manipulation and search functionality
library country_helper_custom;

import 'package:towasl/core/helpers/custom_countries.dart';

/// Checks if a string is numeric
///
/// Removes any '+' characters before checking
/// Used for validating phone numbers and dial codes
///
/// @param s The string to check
/// @return True if the string contains only numeric characters
bool isNumeric(String s) =>
    s.isNotEmpty && int.tryParse(s.replaceAll("+", "")) != null;

/// Removes diacritical marks from a string
///
/// Replaces accented characters with their non-accented equivalents
/// Used for normalizing text for search and comparison
///
/// @param str The string to process
/// @return The string with diacritical marks removed
String removeDiacritics(String str) {
  // Characters with diacritical marks
  var withDia =
      'ÀÁÂÃÄÅàáâãäåÒÓÔÕÕÖØòóôõöøÈÉÊËèéêëðÇçÐÌÍÎÏìíîïÙÚÛÜùúûüÑñŠšŸÿýŽž';

  // Equivalent characters without diacritical marks
  var withoutDia =
      'AAAAAAaaaaaaOOOOOOOooooooEEEEeeeeeCcDIIIIiiiiUUUUuuuuNnSsYyyZz';

  // Replace each character with its non-diacritical equivalent
  for (int i = 0; i < withDia.length; i++) {
    str = str.replaceAll(withDia[i], withoutDia[i]);
  }

  return str;
}

/// Extension methods for List<Country>
///
/// Adds additional functionality to lists of Country objects
extension CountryExtensions on List<Country> {
  /// Searches for countries matching a search string
  ///
  /// Handles both numeric searches (for dial codes) and text searches (for names)
  /// Normalizes text by removing diacritics and converting to lowercase
  ///
  /// @param search The search string
  /// @return A filtered list of countries matching the search criteria
  List<Country> stringSearch(String search) {
    // Normalize the search string
    search = removeDiacritics(search.toLowerCase());

    return where(
      (country) => isNumeric(search) || search.startsWith("+")
          // If search is numeric or starts with +, search in dial codes
          ? country.dialCode.contains(search)
          // Otherwise search in country names (both English and translations)
          : removeDiacritics(country.name.replaceAll("+", "").toLowerCase())
                  .contains(search) ||
              country.nameTranslations.values.any((element) =>
                  removeDiacritics(element.toLowerCase()).contains(search)),
    ).toList();
  }
}
