/// Profile Validation Helper
///
/// Provides validation functions for user profile data
/// Used throughout the application for form validation
library profile_validation;

import 'package:get/get.dart';
import 'package:intl_phone_field/phone_number.dart';

/// Validation Functions
///
/// Static methods for validating different types of user input
/// Returns translated error messages or null if validation passes
class VALIDATIONS {
  /// Validates that a field is not empty
  ///
  /// @param value The string to validate
  /// @return An error message if empty, null otherwise
  static String? validateRequired(String? value) {
    if (value!.isEmpty) {
      return "required".tr;
    } else {
      return null;
    }
  }

  /// Validates a birth year
  ///
  /// Checks that:
  /// - The field is not empty
  /// - The year is a 4-digit number
  /// - The age is between 18 and 120 years
  ///
  /// @param value The year string to validate
  /// @return An error message if invalid, null otherwise
  static String? validateYear(String? value) {
    if (value!.isEmpty) {
      return "required".tr;
    } else if (value.length != 4) {
      return "Invalid year".tr;
    } else if (DateTime.now().year - int.parse(value.toString()) < 18 ||
        DateTime.now().year - int.parse(value.toString()) > 120) {
      return "Incorrect".tr;
    } else {
      return null;
    }
  }

  /// Validates a phone number
  ///
  /// Checks that:
  /// - The phone number is not null
  /// - The phone number matches the Saudi format (05xxxxxxxx)
  ///
  /// @param value The PhoneNumber object to validate
  /// @return An error message if invalid, null otherwise
  static String? validatePhone(PhoneNumber? value) {
    // Saudi phone number format: 05xxxxxxxx (10 digits total)
    RegExp regex = RegExp(r"^05[0-9]{8}$");

    if (value == null) return "required".tr;

    if (!regex.hasMatch(value.number)) {
      return 'mobile_format'.tr;
    } else {
      return null;
    }
  }
}
