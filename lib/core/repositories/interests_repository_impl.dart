/// Interests Repository Implementation
///
/// Implements the InterestsRepository interface using Firebase services
/// Provides concrete implementation for interests data operations
library interests_repository_impl;

import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:towasl/core/repositories/interests_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/models/interest_model.dart';

/// Interests Repository Implementation
///
/// Implements the InterestsRepository interface using FirebaseService
class InterestsRepositoryImpl implements InterestsRepository {
  final FirebaseService _firebaseService;

  /// Constructor that takes a FirebaseService instance
  ///
  /// @param firebaseService The Firebase service for database operations
  InterestsRepositoryImpl({
    required FirebaseService firebaseService,
  }) : _firebaseService = firebaseService;

  @override
  Future<List<InterestModel>> getAllInterests() async {
    try {
      final snapshot = await getInterestsOrderedByDisplayOrder();

      if (kDebugMode) {
        print("Raw snapshot docs count: ${snapshot.docs.length}");
      }

      List<Map<dynamic, dynamic>> list = snapshot.docs
          .map((doc) => doc.data() as Map<dynamic, dynamic>)
          .toList();

      if (kDebugMode) {
        print("Converted list length: ${list.length}");
        print("Raw data: ${jsonEncode(list)}");
      }

      final interests = interestModelFromJson(jsonEncode(list));

      if (kDebugMode) {
        print("Parsed interests count: ${interests.length}");
      }

      return interests;
    } catch (e) {
      if (kDebugMode) print("Error getting all interests: $e");
      rethrow;
    }
  }

  @override
  Future<Map<String, dynamic>?> getInterestCategoryById(
      String categoryId) async {
    try {
      final snapshot =
          await _firebaseService.getDocument("interests", categoryId.trim());

      if (snapshot.exists) {
        return snapshot.data() as Map<String, dynamic>?;
      }
      return null;
    } catch (e) {
      if (kDebugMode) print("Error getting interest category: $e");
      rethrow;
    }
  }

  @override
  Future<QuerySnapshot> getInterestsOrderedByDisplayOrder() async {
    try {
      return await _firebaseService.firestore
          .collection("interests")
          .orderBy("order_number", descending: false)
          .get();
    } catch (e) {
      if (kDebugMode) {
        print("Error getting interests ordered by display order: $e");
      }
      rethrow;
    }
  }

  @override
  Future<List<InterestModel>> getVisibleInterests() async {
    try {
      final allInterests = await getAllInterests();

      // Remove any categories marked as not shown
      return allInterests.where((element) => element.isShown != false).toList();
    } catch (e) {
      if (kDebugMode) {
        print("Error getting visible interests: $e");
      }
      rethrow;
    }
  }
}
