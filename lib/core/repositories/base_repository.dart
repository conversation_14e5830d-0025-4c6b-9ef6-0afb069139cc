import 'package:flutter/foundation.dart';
import 'package:towasl/core/repositories/repository_error.dart';

/// Base Repository
///
/// Provides common functionality for all repositories including
/// standardized error handling, logging, and operation tracking
abstract class BaseRepository {
  /// Execute an operation with standardized error handling
  ///
  /// Wraps repository operations with consistent error handling,
  /// logging, and retry mechanisms
  ///
  /// @param operation The operation to execute
  /// @param operationName Name of the operation for logging
  /// @param retryCount Number of retry attempts (default: 0)
  /// @return The result of the operation
  Future<T> executeWithErrorHandling<T>(
    Future<T> Function() operation,
    String operationName, {
    int retryCount = 0,
  }) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts <= retryCount) {
      try {
        if (kDebugMode && attempts > 0) {
          if (kDebugMode) {
            print('Repository: Retry attempt $attempts for $operationName');
          }
        }

        _logOperation(operationName, 'started', attempts);

        final result = await operation();

        _logOperation(operationName, 'completed', attempts);
        return result;
      } catch (e) {
        attempts++;
        lastException = e is Exception ? e : Exception(e.toString());

        _logError(operationName, e, attempts);

        if (attempts > retryCount) {
          // All retry attempts exhausted
          throw RepositoryError(
            'Operation failed after $attempts attempts',
            operationName,
            lastException,
          );
        }

        // Wait before retry with exponential backoff
        if (attempts <= retryCount) {
          final delay = Duration(milliseconds: 500 * attempts);
          await Future.delayed(delay);
        }
      }
    }

    // This should never be reached, but included for completeness
    throw RepositoryError(
      'Unexpected error in operation execution',
      operationName,
      lastException,
    );
  }

  /// Execute an operation with error handling but no retries
  ///
  /// For operations that should not be retried (like writes that might cause duplicates)
  ///
  /// @param operation The operation to execute
  /// @param operationName Name of the operation for logging
  /// @return The result of the operation
  Future<T> executeWithoutRetry<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    return executeWithErrorHandling(operation, operationName, retryCount: 0);
  }

  /// Execute a read operation with retries
  ///
  /// For read operations that can safely be retried
  ///
  /// @param operation The operation to execute
  /// @param operationName Name of the operation for logging
  /// @return The result of the operation
  Future<T> executeReadOperation<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    return executeWithErrorHandling(operation, operationName, retryCount: 2);
  }

  /// Execute a write operation without retries
  ///
  /// For write operations that should not be retried to avoid duplicates
  ///
  /// @param operation The operation to execute
  /// @param operationName Name of the operation for logging
  /// @return The result of the operation
  Future<T> executeWriteOperation<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    return executeWithoutRetry(operation, operationName);
  }

  /// Validate input parameters before executing operations
  ///
  /// @param parameters Map of parameter names to values
  /// @param operationName Name of the operation for error context
  void validateParameters(
    Map<String, dynamic> parameters,
    String operationName,
  ) {
    for (final entry in parameters.entries) {
      final key = entry.key;
      final value = entry.value;

      if (value == null) {
        throw RepositoryError(
          'Required parameter "$key" is null',
          operationName,
        );
      }

      if (value is String && value.isEmpty) {
        throw RepositoryError(
          'Required parameter "$key" is empty',
          operationName,
        );
      }

      if (value is List && value.isEmpty) {
        throw RepositoryError(
          'Required parameter "$key" is empty list',
          operationName,
        );
      }

      if (value is Map && value.isEmpty) {
        throw RepositoryError(
          'Required parameter "$key" is empty map',
          operationName,
        );
      }
    }
  }

  /// Log operation start/completion
  void _logOperation(String operationName, String status, int attempt) {
    if (kDebugMode) {
      final attemptInfo = attempt > 0 ? ' (attempt ${attempt + 1})' : '';
      print('Repository: $operationName $status$attemptInfo');
    }
  }

  /// Log operation errors
  void _logError(String operationName, dynamic error, int attempt) {
    if (kDebugMode) {
      final attemptInfo = attempt > 0 ? ' (attempt $attempt)' : '';
      print('Repository Error: $operationName failed$attemptInfo - $error');
    }
  }

  /// Get repository name for logging
  String get repositoryName => runtimeType.toString();

  /// Check if an error is retryable
  ///
  /// Determines if an operation should be retried based on the error type
  ///
  /// @param error The error to check
  /// @return True if the error is retryable, false otherwise
  bool isRetryableError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    // Network-related errors that can be retried
    if (errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('connection') ||
        errorString.contains('unavailable') ||
        errorString.contains('deadline exceeded')) {
      return true;
    }

    // Firebase-specific retryable errors
    if (errorString.contains('permission-denied') ||
        errorString.contains('unauthenticated') ||
        errorString.contains('not-found')) {
      return false; // These should not be retried
    }

    // Default to not retryable for unknown errors
    return false;
  }

  /// Create a standardized error message
  ///
  /// @param operation The operation that failed
  /// @param originalError The original error
  /// @return A standardized error message
  String createErrorMessage(String operation, dynamic originalError) {
    return 'Failed to $operation: ${originalError?.toString() ?? 'Unknown error'}';
  }

  /// Log repository metrics for monitoring
  ///
  /// @param operationName Name of the operation
  /// @param duration Duration of the operation
  /// @param success Whether the operation was successful
  void logMetrics(String operationName, Duration duration, bool success) {
    if (kDebugMode) {
      final status = success ? 'SUCCESS' : 'FAILURE';
      print(
          'Repository Metrics: $repositoryName.$operationName - $status - ${duration.inMilliseconds}ms');
    }
  }

  /// Execute operation with metrics tracking
  ///
  /// @param operation The operation to execute
  /// @param operationName Name of the operation for logging
  /// @return The result of the operation
  Future<T> executeWithMetrics<T>(
    Future<T> Function() operation,
    String operationName,
  ) async {
    final stopwatch = Stopwatch()..start();
    bool success = false;

    try {
      final result = await operation();
      success = true;
      return result;
    } finally {
      stopwatch.stop();
      logMetrics(operationName, stopwatch.elapsed, success);
    }
  }
}
