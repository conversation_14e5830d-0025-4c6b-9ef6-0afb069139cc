import 'package:towasl/core/repositories/auth_repository.dart';
import 'package:towasl/core/repositories/base_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';

/// Authentication Repository Implementation
///
/// Implements the AuthRepository interface using Firebase services
/// Provides concrete implementation for authentication operations
class AuthRepositoryImpl extends BaseRepository implements AuthRepository {
  final FirebaseService _firebaseService;

  /// Constructor with dependency injection
  ///
  /// @param firebaseService The Firebase service for database operations
  AuthRepositoryImpl({
    required FirebaseService firebaseService,
  }) : _firebaseService = firebaseService;

  @override
  Future<String?> getUserIdByMobile(String mobile) async {
    return executeReadOperation(() async {
      validateParameters({'mobile': mobile}, 'getUserIdByMobile');

      final snapshot = await _firebaseService.firestore
          .collection("users")
          .where("mobile", isEqualTo: mobile)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        final userData = snapshot.docs.first.data();
        return userData['user_id'] as String?;
      }
      return null;
    }, 'getUserIdByMobile');
  }

  @override
  Future<bool> userExists(String mobile) async {
    return executeReadOperation(() async {
      validateParameters({'mobile': mobile}, 'userExists');

      final userId = await getUserIdByMobile(mobile);
      return userId != null;
    }, 'userExists');
  }

  @override
  Future<void> createUserSession(String userId, String sessionToken) async {
    return executeWriteOperation(() async {
      validateParameters({
        'userId': userId,
        'sessionToken': sessionToken,
      }, 'createUserSession');

      await _firebaseService.updateDocument("users", userId, {
        'session_token': sessionToken,
        'last_login': DateTime.now().toIso8601String(),
        'session_created_at': DateTime.now().toIso8601String(),
      });
    }, 'createUserSession');
  }

  @override
  Future<void> invalidateSession(String userId) async {
    return executeWriteOperation(() async {
      validateParameters({'userId': userId}, 'invalidateSession');

      await _firebaseService.updateDocument("users", userId, {
        'session_token': null,
        'session_invalidated_at': DateTime.now().toIso8601String(),
      });
    }, 'invalidateSession');
  }

  @override
  Future<bool> isSessionValid(String userId, String sessionToken) async {
    return executeReadOperation(() async {
      validateParameters({
        'userId': userId,
        'sessionToken': sessionToken,
      }, 'isSessionValid');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) return false;

      final userData = snapshot.data() as Map<String, dynamic>;
      final storedToken = userData['session_token'] as String?;

      return storedToken == sessionToken && storedToken != null;
    }, 'isSessionValid');
  }

  @override
  Future<void> updateUserBlockStatus(String userId, bool isBlocked) async {
    return executeWriteOperation(() async {
      validateParameters({'userId': userId}, 'updateUserBlockStatus');

      await _firebaseService.updateDocument("users", userId, {
        'is_blocked': isBlocked,
        'block_status_updated_at': DateTime.now().toIso8601String(),
      });
    }, 'updateUserBlockStatus');
  }

  @override
  Stream<bool> getUserBlockStatus(String userId) {
    return _firebaseService.firestore
        .collection("users")
        .where("is_blocked", isEqualTo: true)
        .where("user_id", isEqualTo: userId)
        .snapshots()
        .map((snapshot) => snapshot.docs.isNotEmpty);
  }

  @override
  Future<void> createUserAccount(
    String userId,
    String mobile, {
    Map<String, dynamic>? additionalData,
  }) async {
    return executeWriteOperation(() async {
      validateParameters({
        'userId': userId,
        'mobile': mobile,
      }, 'createUserAccount');

      final userData = {
        "mobile": mobile,
        "user_id": userId,
        "is_blocked": false,
        "create_at": DateTime.now().toIso8601String(),
        "account_active": true,
        ...?additionalData,
      };

      await _firebaseService.setDocument("users", userId, userData);
    }, 'createUserAccount');
  }

  @override
  Future<void> updateFcmToken(String userId, String fcmToken) async {
    return executeWriteOperation(() async {
      validateParameters({
        'userId': userId,
        'fcmToken': fcmToken,
      }, 'updateFcmToken');

      await _firebaseService.updateDocument("users", userId, {
        'fcm_token': fcmToken,
        'fcm_token_updated_at': DateTime.now().toIso8601String(),
      });
    }, 'updateFcmToken');
  }

  @override
  Future<String?> getFcmToken(String userId) async {
    return executeReadOperation(() async {
      validateParameters({'userId': userId}, 'getFcmToken');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) return null;

      final userData = snapshot.data() as Map<String, dynamic>;
      return userData['fcm_token'] as String?;
    }, 'getFcmToken');
  }

  @override
  Future<void> updateSessionToken(String userId, String sessionToken) async {
    return executeWriteOperation(() async {
      validateParameters({
        'userId': userId,
        'sessionToken': sessionToken,
      }, 'updateSessionToken');

      await _firebaseService.updateDocument("users", userId, {
        'session_token': sessionToken,
        'session_token_updated_at': DateTime.now().toIso8601String(),
      });
    }, 'updateSessionToken');
  }

  @override
  Future<String?> getSessionToken(String userId) async {
    return executeReadOperation(() async {
      validateParameters({'userId': userId}, 'getSessionToken');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) return null;

      final userData = snapshot.data() as Map<String, dynamic>;
      return userData['session_token'] as String?;
    }, 'getSessionToken');
  }

  @override
  Future<void> clearUserSession(String userId) async {
    return executeWriteOperation(() async {
      validateParameters({'userId': userId}, 'clearUserSession');

      await _firebaseService.updateDocument("users", userId, {
        'session_token': null,
        'fcm_token': null,
        'session_cleared_at': DateTime.now().toIso8601String(),
      });
    }, 'clearUserSession');
  }

  @override
  Future<bool> isAccountActive(String userId) async {
    return executeReadOperation(() async {
      validateParameters({'userId': userId}, 'isAccountActive');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) return false;

      final userData = snapshot.data() as Map<String, dynamic>;
      return userData['account_active'] == true &&
          userData['is_blocked'] != true;
    }, 'isAccountActive');
  }

  @override
  Future<DateTime?> getLastLoginTime(String userId) async {
    return executeReadOperation(() async {
      validateParameters({'userId': userId}, 'getLastLoginTime');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) return null;

      final userData = snapshot.data() as Map<String, dynamic>;
      final lastLoginStr = userData['last_login'] as String?;

      if (lastLoginStr != null) {
        return DateTime.tryParse(lastLoginStr);
      }
      return null;
    }, 'getLastLoginTime');
  }

  @override
  Future<void> updateLastLoginTime(String userId, {DateTime? loginTime}) async {
    return executeWriteOperation(() async {
      validateParameters({'userId': userId}, 'updateLastLoginTime');

      final time = loginTime ?? DateTime.now();

      await _firebaseService.updateDocument("users", userId, {
        'last_login': time.toIso8601String(),
      });
    }, 'updateLastLoginTime');
  }

  @override
  Future<DateTime?> getAccountCreationTime(String userId) async {
    return executeReadOperation(() async {
      validateParameters({'userId': userId}, 'getAccountCreationTime');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) return null;

      final userData = snapshot.data() as Map<String, dynamic>;
      final createAtStr = userData['create_at'] as String?;

      if (createAtStr != null) {
        return DateTime.tryParse(createAtStr);
      }
      return null;
    }, 'getAccountCreationTime');
  }

  @override
  Future<String?> validateCredentials(
    String mobile, {
    Map<String, dynamic>? additionalCredentials,
  }) async {
    return executeReadOperation(() async {
      validateParameters({'mobile': mobile}, 'validateCredentials');

      // For now, just check if user exists by mobile
      // Additional credential validation can be added here
      return await getUserIdByMobile(mobile);
    }, 'validateCredentials');
  }

  @override
  Future<Map<String, dynamic>> getAuthStats(String userId) async {
    return executeReadOperation(() async {
      validateParameters({'userId': userId}, 'getAuthStats');

      final snapshot = await _firebaseService.getDocument("users", userId);

      if (!snapshot.exists) {
        return {'exists': false};
      }

      final userData = snapshot.data() as Map<String, dynamic>;

      return {
        'exists': true,
        'isBlocked': userData['is_blocked'] == true,
        'isActive': userData['account_active'] == true,
        'hasSession': userData['session_token'] != null,
        'hasFcmToken': userData['fcm_token'] != null,
        'lastLogin': userData['last_login'],
        'createdAt': userData['create_at'],
      };
    }, 'getAuthStats');
  }
}
