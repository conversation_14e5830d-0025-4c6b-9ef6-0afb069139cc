import 'package:flutter/foundation.dart';

/// Cache Entry
///
/// Represents a cached data entry with expiration time
class CacheEntry<T> {
  /// The cached data
  final T data;

  /// When the data was cached
  final DateTime cachedAt;

  /// Time-to-live for the cached data
  final Duration? ttl;

  /// Constructor for CacheEntry
  ///
  /// @param data The data to cache
  /// @param cachedAt When the data was cached
  /// @param ttl Time-to-live for the cache entry
  CacheEntry(this.data, this.cachedAt, this.ttl);

  /// Check if the cache entry is expired
  ///
  /// @return True if the entry is expired, false otherwise
  bool get isExpired {
    if (ttl == null) return false;
    return DateTime.now().difference(cachedAt) > ttl!;
  }

  /// Get the remaining time before expiration
  ///
  /// @return Duration until expiration, or null if no TTL
  Duration? get timeUntilExpiration {
    if (ttl == null) return null;
    final elapsed = DateTime.now().difference(cachedAt);
    final remaining = ttl! - elapsed;
    return remaining.isNegative ? Duration.zero : remaining;
  }
}

/// Cached Repository Mixin
///
/// Provides caching capabilities to repositories
/// Supports TTL-based expiration and cache management
mixin CachedRepository<T> {
  /// Internal cache storage
  final Map<String, CacheEntry<T>> _cache = {};

  /// Default TTL for cache entries (5 minutes)
  Duration get defaultTtl => const Duration(minutes: 5);

  /// Maximum cache size (number of entries)
  int get maxCacheSize => 100;

  /// Get data from cache
  ///
  /// @param key The cache key
  /// @return The cached data or null if not found/expired
  T? getCached(String key) {
    final entry = _cache[key];

    if (entry == null) {
      if (kDebugMode) {
        print('Cache MISS: $key (not found)');
      }
      return null;
    }

    if (entry.isExpired) {
      if (kDebugMode) {
        print('Cache MISS: $key (expired)');
      }
      _cache.remove(key);
      return null;
    }

    if (kDebugMode) {
      print('Cache HIT: $key');
    }
    return entry.data;
  }

  /// Set data in cache
  ///
  /// @param key The cache key
  /// @param data The data to cache
  /// @param ttl Time-to-live for the cache entry (optional)
  void setCached(String key, T data, {Duration? ttl}) {
    // Enforce cache size limit
    if (_cache.length >= maxCacheSize) {
      _evictOldestEntry();
    }

    final effectiveTtl = ttl ?? defaultTtl;
    _cache[key] = CacheEntry(data, DateTime.now(), effectiveTtl);

    if (kDebugMode) {
      print('Cache SET: $key (TTL: ${effectiveTtl.inMinutes}m)');
    }
  }

  /// Remove data from cache
  ///
  /// @param key The cache key to remove
  /// @return True if the key was found and removed, false otherwise
  bool removeCached(String key) {
    final removed = _cache.remove(key) != null;

    if (kDebugMode) {
      print('Cache REMOVE: $key (found: $removed)');
    }

    return removed;
  }

  /// Clear all cached data
  void clearCache() {
    final count = _cache.length;
    _cache.clear();

    if (kDebugMode) {
      print('Cache CLEAR: Removed $count entries');
    }
  }

  /// Check if a key exists in cache (and is not expired)
  ///
  /// @param key The cache key to check
  /// @return True if the key exists and is not expired
  bool hasCached(String key) {
    final entry = _cache[key];
    if (entry == null) return false;

    if (entry.isExpired) {
      _cache.remove(key);
      return false;
    }

    return true;
  }

  /// Get cache statistics
  ///
  /// @return Map containing cache statistics
  Map<String, dynamic> getCacheStats() {
    int expiredCount = 0;
    int validCount = 0;

    for (final entry in _cache.values) {
      if (entry.isExpired) {
        expiredCount++;
      } else {
        validCount++;
      }
    }

    return {
      'totalEntries': _cache.length,
      'validEntries': validCount,
      'expiredEntries': expiredCount,
      'maxSize': maxCacheSize,
      'utilizationPercent': (_cache.length / maxCacheSize * 100).round(),
    };
  }

  /// Clean up expired entries
  ///
  /// @return Number of entries removed
  int cleanupExpiredEntries() {
    final keysToRemove = <String>[];

    for (final entry in _cache.entries) {
      if (entry.value.isExpired) {
        keysToRemove.add(entry.key);
      }
    }

    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    if (kDebugMode && keysToRemove.isNotEmpty) {
      if (kDebugMode) {
        print('Cache CLEANUP: Removed ${keysToRemove.length} expired entries');
      }
    }

    return keysToRemove.length;
  }

  /// Execute operation with caching
  ///
  /// Checks cache first, executes operation if not cached, then caches result
  ///
  /// @param key The cache key
  /// @param operation The operation to execute if not cached
  /// @param ttl Time-to-live for the cache entry (optional)
  /// @return The data (from cache or operation)
  Future<T> executeWithCache(
    String key,
    Future<T> Function() operation, {
    Duration? ttl,
  }) async {
    // Check cache first
    final cached = getCached(key);
    if (cached != null) {
      return cached;
    }

    // Execute operation and cache result
    final result = await operation();
    setCached(key, result, ttl: ttl);

    return result;
  }

  /// Execute operation with cache and fallback
  ///
  /// Tries cache first, then operation, falls back to stale cache if operation fails
  ///
  /// @param key The cache key
  /// @param operation The operation to execute
  /// @param ttl Time-to-live for the cache entry (optional)
  /// @return The data (from cache, operation, or stale cache)
  Future<T?> executeWithCacheAndFallback(
    String key,
    Future<T> Function() operation, {
    Duration? ttl,
  }) async {
    // Check for valid cache first
    final cached = getCached(key);
    if (cached != null) {
      return cached;
    }

    try {
      // Execute operation and cache result
      final result = await operation();
      setCached(key, result, ttl: ttl);
      return result;
    } catch (e) {
      if (kDebugMode) {
        print(
            'Cache FALLBACK: Operation failed for $key, checking stale cache');
      }

      // Check for stale cache as fallback
      final staleEntry = _cache[key];
      if (staleEntry != null) {
        if (kDebugMode) {
          print('Cache FALLBACK: Using stale data for $key');
        }
        return staleEntry.data;
      }

      // No fallback available, rethrow the error
      rethrow;
    }
  }

  /// Evict the oldest cache entry
  void _evictOldestEntry() {
    if (_cache.isEmpty) return;

    String? oldestKey;
    DateTime? oldestTime;

    for (final entry in _cache.entries) {
      if (oldestTime == null || entry.value.cachedAt.isBefore(oldestTime)) {
        oldestTime = entry.value.cachedAt;
        oldestKey = entry.key;
      }
    }

    if (oldestKey != null) {
      _cache.remove(oldestKey);
      if (kDebugMode) {
        print('Cache EVICT: Removed oldest entry $oldestKey');
      }
    }
  }

  /// Generate cache key from parameters
  ///
  /// @param prefix Key prefix (usually operation name)
  /// @param parameters Parameters to include in the key
  /// @return Generated cache key
  String generateCacheKey(String prefix, Map<String, dynamic> parameters) {
    final sortedParams = Map.fromEntries(
      parameters.entries.toList()..sort((a, b) => a.key.compareTo(b.key)),
    );

    final paramString =
        sortedParams.entries.map((e) => '${e.key}:${e.value}').join('|');

    return '$prefix:$paramString';
  }
}
