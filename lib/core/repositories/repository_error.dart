/// Repository Error
/// 
/// Standardized error class for repository operations
/// Provides consistent error handling across all repositories
class RepositoryError extends Error {
  /// The error message describing what went wrong
  final String message;
  
  /// The operation that was being performed when the error occurred
  final String operation;
  
  /// The original error that caused this repository error (if any)
  final dynamic originalError;
  
  /// The error code for categorizing different types of errors
  final String? errorCode;
  
  /// Additional context information about the error
  final Map<String, dynamic>? context;

  /// Constructor for RepositoryError
  /// 
  /// @param message The error message
  /// @param operation The operation that failed
  /// @param originalError The original error (optional)
  /// @param errorCode Error code for categorization (optional)
  /// @param context Additional context information (optional)
  RepositoryError(
    this.message,
    this.operation, [
    this.originalError,
    this.errorCode,
    this.context,
  ]);

  /// Create a network-related error
  /// 
  /// @param operation The operation that failed
  /// @param originalError The original network error
  /// @return A RepositoryError with network error context
  factory RepositoryError.network(String operation, dynamic originalError) {
    return RepositoryError(
      'Network error occurred during $operation',
      operation,
      originalError,
      'NETWORK_ERROR',
      {'type': 'network', 'retryable': true},
    );
  }

  /// Create a permission-related error
  /// 
  /// @param operation The operation that failed
  /// @param originalError The original permission error
  /// @return A RepositoryError with permission error context
  factory RepositoryError.permission(String operation, dynamic originalError) {
    return RepositoryError(
      'Permission denied for $operation',
      operation,
      originalError,
      'PERMISSION_DENIED',
      {'type': 'permission', 'retryable': false},
    );
  }

  /// Create a validation error
  /// 
  /// @param operation The operation that failed
  /// @param field The field that failed validation
  /// @param value The invalid value
  /// @return A RepositoryError with validation error context
  factory RepositoryError.validation(
    String operation,
    String field,
    dynamic value,
  ) {
    return RepositoryError(
      'Validation failed for $field in $operation',
      operation,
      null,
      'VALIDATION_ERROR',
      {
        'type': 'validation',
        'field': field,
        'value': value?.toString(),
        'retryable': false,
      },
    );
  }

  /// Create a not found error
  /// 
  /// @param operation The operation that failed
  /// @param resourceId The ID of the resource that was not found
  /// @return A RepositoryError with not found error context
  factory RepositoryError.notFound(String operation, String resourceId) {
    return RepositoryError(
      'Resource not found during $operation',
      operation,
      null,
      'NOT_FOUND',
      {
        'type': 'not_found',
        'resourceId': resourceId,
        'retryable': false,
      },
    );
  }

  /// Create a timeout error
  /// 
  /// @param operation The operation that failed
  /// @param timeoutDuration The timeout duration that was exceeded
  /// @return A RepositoryError with timeout error context
  factory RepositoryError.timeout(String operation, Duration timeoutDuration) {
    return RepositoryError(
      'Operation timed out during $operation',
      operation,
      null,
      'TIMEOUT',
      {
        'type': 'timeout',
        'timeoutMs': timeoutDuration.inMilliseconds,
        'retryable': true,
      },
    );
  }

  /// Create a data corruption error
  /// 
  /// @param operation The operation that failed
  /// @param details Details about the data corruption
  /// @return A RepositoryError with data corruption error context
  factory RepositoryError.dataCorruption(String operation, String details) {
    return RepositoryError(
      'Data corruption detected during $operation',
      operation,
      null,
      'DATA_CORRUPTION',
      {
        'type': 'data_corruption',
        'details': details,
        'retryable': false,
      },
    );
  }

  /// Create a quota exceeded error
  /// 
  /// @param operation The operation that failed
  /// @param quotaType The type of quota that was exceeded
  /// @return A RepositoryError with quota exceeded error context
  factory RepositoryError.quotaExceeded(String operation, String quotaType) {
    return RepositoryError(
      'Quota exceeded during $operation',
      operation,
      null,
      'QUOTA_EXCEEDED',
      {
        'type': 'quota_exceeded',
        'quotaType': quotaType,
        'retryable': false,
      },
    );
  }

  /// Check if this error is retryable
  /// 
  /// @return True if the operation can be retried, false otherwise
  bool get isRetryable {
    return context?['retryable'] == true;
  }

  /// Get the error type
  /// 
  /// @return The type of error (network, permission, validation, etc.)
  String? get errorType {
    return context?['type'];
  }

  /// Get a user-friendly error message
  /// 
  /// @return A message suitable for displaying to users
  String get userFriendlyMessage {
    switch (errorCode) {
      case 'NETWORK_ERROR':
        return 'Network connection error. Please check your internet connection and try again.';
      case 'PERMISSION_DENIED':
        return 'You do not have permission to perform this action.';
      case 'VALIDATION_ERROR':
        return 'Invalid data provided. Please check your input and try again.';
      case 'NOT_FOUND':
        return 'The requested information could not be found.';
      case 'TIMEOUT':
        return 'The operation took too long to complete. Please try again.';
      case 'DATA_CORRUPTION':
        return 'Data integrity issue detected. Please contact support.';
      case 'QUOTA_EXCEEDED':
        return 'Service limit exceeded. Please try again later.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  /// Convert error to a map for logging or serialization
  /// 
  /// @return A map representation of the error
  Map<String, dynamic> toMap() {
    return {
      'message': message,
      'operation': operation,
      'errorCode': errorCode,
      'originalError': originalError?.toString(),
      'context': context,
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  @override
  String toString() {
    final buffer = StringBuffer();
    buffer.write('RepositoryError: $message');
    
    if (errorCode != null) {
      buffer.write(' (Code: $errorCode)');
    }
    
    buffer.write(' during operation: $operation');
    
    if (originalError != null) {
      buffer.write('\nOriginal error: $originalError');
    }
    
    if (context != null && context!.isNotEmpty) {
      buffer.write('\nContext: $context');
    }
    
    return buffer.toString();
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is RepositoryError &&
        other.message == message &&
        other.operation == operation &&
        other.errorCode == errorCode;
  }

  @override
  int get hashCode {
    return Object.hash(message, operation, errorCode);
  }
}
