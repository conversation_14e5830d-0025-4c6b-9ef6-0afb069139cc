/// Authentication Repository
/// 
/// Provides methods for authentication-related data operations
/// Abstracts authentication logic from controllers
abstract class AuthRepository {
  /// Check if a user exists by mobile number
  /// 
  /// @param mobile The mobile number to check
  /// @return A Future that resolves to the user ID if found, null otherwise
  Future<String?> getUserIdByMobile(String mobile);

  /// Check if user exists by mobile number (boolean result)
  /// 
  /// @param mobile The mobile number to check
  /// @return A Future that resolves to true if user exists, false otherwise
  Future<bool> userExists(String mobile);

  /// Create a new user session
  /// 
  /// @param userId The user ID
  /// @param sessionToken The session token to create
  /// @return A Future that completes when the session is created
  Future<void> createUserSession(String userId, String sessionToken);

  /// Invalidate a user session
  /// 
  /// @param userId The user ID
  /// @return A Future that completes when the session is invalidated
  Future<void> invalidateSession(String userId);

  /// Check if a session is valid
  /// 
  /// @param userId The user ID
  /// @param sessionToken The session token to validate
  /// @return A Future that resolves to true if session is valid, false otherwise
  Future<bool> isSessionValid(String userId, String sessionToken);

  /// Update user's blocked status
  /// 
  /// @param userId The user ID
  /// @param isBlocked Whether the user should be blocked
  /// @return A Future that completes when the status is updated
  Future<void> updateUserBlockStatus(String userId, bool isBlocked);

  /// Get user's blocked status
  /// 
  /// @param userId The user ID
  /// @return A Stream that emits the user's blocked status
  Stream<bool> getUserBlockStatus(String userId);

  /// Create a new user account
  /// 
  /// @param userId The unique user ID
  /// @param mobile The user's mobile number
  /// @param additionalData Any additional user data
  /// @return A Future that completes when the user is created
  Future<void> createUserAccount(
    String userId,
    String mobile, {
    Map<String, dynamic>? additionalData,
  });

  /// Update user's FCM token for push notifications
  /// 
  /// @param userId The user ID
  /// @param fcmToken The FCM token
  /// @return A Future that completes when the token is updated
  Future<void> updateFcmToken(String userId, String fcmToken);

  /// Get user's current FCM token
  /// 
  /// @param userId The user ID
  /// @return A Future that resolves to the FCM token or null if not found
  Future<String?> getFcmToken(String userId);

  /// Update user's session token
  /// 
  /// @param userId The user ID
  /// @param sessionToken The new session token
  /// @return A Future that completes when the token is updated
  Future<void> updateSessionToken(String userId, String sessionToken);

  /// Get user's current session token
  /// 
  /// @param userId The user ID
  /// @return A Future that resolves to the session token or null if not found
  Future<String?> getSessionToken(String userId);

  /// Clear all session data for a user
  /// 
  /// @param userId The user ID
  /// @return A Future that completes when session data is cleared
  Future<void> clearUserSession(String userId);

  /// Check if user account is active
  /// 
  /// @param userId The user ID
  /// @return A Future that resolves to true if account is active, false otherwise
  Future<bool> isAccountActive(String userId);

  /// Get user's last login time
  /// 
  /// @param userId The user ID
  /// @return A Future that resolves to the last login DateTime or null
  Future<DateTime?> getLastLoginTime(String userId);

  /// Update user's last login time
  /// 
  /// @param userId The user ID
  /// @param loginTime The login time (defaults to now)
  /// @return A Future that completes when the time is updated
  Future<void> updateLastLoginTime(String userId, {DateTime? loginTime});

  /// Get user's account creation time
  /// 
  /// @param userId The user ID
  /// @return A Future that resolves to the creation DateTime or null
  Future<DateTime?> getAccountCreationTime(String userId);

  /// Validate user credentials
  /// 
  /// @param mobile The mobile number
  /// @param additionalCredentials Any additional credentials to validate
  /// @return A Future that resolves to the user ID if valid, null otherwise
  Future<String?> validateCredentials(
    String mobile, {
    Map<String, dynamic>? additionalCredentials,
  });

  /// Get authentication statistics for a user
  /// 
  /// @param userId The user ID
  /// @return A Future that resolves to authentication statistics
  Future<Map<String, dynamic>> getAuthStats(String userId);
}
