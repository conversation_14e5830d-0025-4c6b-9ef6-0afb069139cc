/// User Repository Implementation
///
/// Implements the UserRepository interface using Firebase services
/// Provides concrete implementation for user data operations
library user_repository_impl;

import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/models/user_model.dart';

/// User Repository Implementation
///
/// Implements the UserRepository interface using FirebaseService
class UserRepositoryImpl implements UserRepository {
  final FirebaseService _firebaseService;

  /// Constructor that takes a FirebaseService instance
  ///
  /// @param firebaseService The Firebase service for database operations
  UserRepositoryImpl({
    required FirebaseService firebaseService,
  }) : _firebaseService = firebaseService;

  @override
  Future<UserModel?> getUserById(String userId) async {
    try {
      final snapshot = await _firebaseService.getDocument("users", userId);

      if (snapshot.exists) {
        return userModelFromJson(jsonEncode(snapshot.data()));
      }
      return null;
    } catch (e) {
      if (kDebugMode) print("Error fetching user data: $e");
      rethrow;
    }
  }

  @override
  Future<void> createUser(String userId, Map<String, dynamic> userData) async {
    try {
      await _firebaseService.setDocument("users", userId, userData);
    } catch (e) {
      if (kDebugMode) print("Error creating user: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateUser(String userId, Map<String, dynamic> userData) async {
    try {
      await _firebaseService.updateDocument("users", userId, userData);
    } catch (e) {
      if (kDebugMode) print("Error updating user: $e");
      rethrow;
    }
  }

  @override
  Future<String?> getUserIdByMobile(String mobile) async {
    try {
      // First try to find user with mobile field
      var snapshot = await _firebaseService.firestore
          .collection("users")
          .where("mobile", isEqualTo: mobile)
          .limit(1)
          .get();

      // If no results, try legacy email field (for backward compatibility)
      if (snapshot.docs.isEmpty) {
        snapshot = await _firebaseService.firestore
            .collection("users")
            .where("email", isEqualTo: mobile)
            .limit(1)
            .get();
      }

      if (snapshot.docs.isNotEmpty) {
        var userData = snapshot.docs.first.data();
        return userData['user_id'];
      }
      return null;
    } catch (e) {
      if (kDebugMode) print("Error checking if user exists: $e");
      rethrow;
    }
  }

  @override
  Stream<bool> isUserBlocked(String userId) {
    return _firebaseService.firestore
        .collection("users")
        .where("is_blocked", isEqualTo: true)
        .where("user_id", isEqualTo: userId)
        .snapshots()
        .map((snapshot) => snapshot.docs.isNotEmpty);
  }

  @override
  Future<void> updateFcmToken(String userId, String fcmToken) async {
    try {
      await _firebaseService.updateDocument("users", userId, {
        'fcm_token': fcmToken,
      });
    } catch (e) {
      if (kDebugMode) print("Error updating FCM token: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateSessionToken(String userId, String sessionToken) async {
    try {
      await _firebaseService.updateDocument("users", userId, {
        'session_token': sessionToken,
      });
    } catch (e) {
      if (kDebugMode) print("Error updating session token: $e");
      rethrow;
    }
  }

  @override
  Future<void> updatePersonalInfo(
      String userId, Map<String, dynamic> personalInfo) async {
    try {
      await _firebaseService.updateDocument("users", userId, personalInfo);
    } catch (e) {
      if (kDebugMode) print("Error updating personal info: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateUserInterests(
      String userId, Map<String, dynamic> interests) async {
    try {
      await _firebaseService.updateDocument("users", userId, interests);
    } catch (e) {
      if (kDebugMode) print("Error updating user interests: $e");
      rethrow;
    }
  }

  @override
  Future<void> updateUserLocation(
      String userId, Map<String, dynamic> location) async {
    try {
      await _firebaseService.updateDocument("users", userId, location);
    } catch (e) {
      if (kDebugMode) print("Error updating user location: $e");
      rethrow;
    }
  }

  @override
  Future<void> deleteUser(String userId) async {
    try {
      await _firebaseService.deleteDocument("users", userId);
    } catch (e) {
      if (kDebugMode) print("Error deleting user: $e");
      rethrow;
    }
  }
}
