/// Dependency Injection Configuration
///
/// Centralizes all dependency injection setup for the application
/// Registers services, repositories, and controllers with GetX
library dependency_injection;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:location/location.dart' as location_lib;
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/auth_controllers/mobile_login_controller.dart';
import 'package:towasl/controllers/auth_controllers/save_register_data_controller.dart';
import 'package:towasl/controllers/interests_controller.dart';
import 'package:towasl/controllers/location_controller.dart';
import 'package:towasl/controllers/match_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/controllers/user/user_data_controller.dart';
import 'package:towasl/controllers/user/user_form_controller.dart';
import 'package:towasl/controllers/user/user_session_controller.dart';
import 'package:towasl/core/repositories/auth_repository.dart';
import 'package:towasl/core/repositories/auth_repository_impl.dart';
import 'package:towasl/core/repositories/interests_repository.dart';
import 'package:towasl/core/repositories/interests_repository_impl.dart';
import 'package:towasl/core/repositories/match_repository.dart';
import 'package:towasl/core/repositories/match_repository_impl.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/repositories/user_repository_impl.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/location_service.dart';
import 'package:towasl/core/services/navigation_service.dart';
import 'package:towasl/core/services/navigation_service_impl.dart';
import 'package:towasl/core/services/auth_navigation_service.dart';
import 'package:towasl/core/services/settings_navigation_service.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/localization/language_controller.dart';

/// Initialize all dependencies
///
/// This method should be called before the app starts
/// It registers all services and controllers needed by the app
Future<void> initDependencies() async {
  // Initialize GetStorage
  await GetStorage.init();

  // Register services
  _registerServices();

  // Register repositories
  _registerRepositories();

  // Register controllers
  _registerControllers();
}

/// Register all services
///
/// Services are registered as lazySingletons by default
/// This means they are only created when first accessed
void _registerServices() {
  // Core services
  Get.lazyPut<StorageService>(() => StorageServiceImpl(GetStorage()),
      fenix: true);

  // Firebase services
  Get.lazyPut<FirebaseService>(
      () => FirebaseServiceImpl(
            firestore: FirebaseFirestore.instance,
            messaging: FirebaseMessaging.instance,
          ),
      fenix: true);

  // Location service
  Get.lazyPut<LocationService>(
      () => LocationServiceImpl(
            location: location_lib.Location(),
          ),
      fenix: true);

  // Navigation services
  Get.lazyPut<NavigationService>(() => NavigationServiceImpl(), fenix: true);

  Get.lazyPut<AuthNavigationService>(
      () => AuthNavigationService(
            navigationService: Get.find<NavigationService>(),
          ),
      fenix: true);

  Get.lazyPut<SettingsNavigationService>(
      () => SettingsNavigationService(
            navigationService: Get.find<NavigationService>(),
          ),
      fenix: true);
}

/// Register all repositories
///
/// Repositories are registered as lazy singletons that persist (fenix: true)
/// They depend on services and provide data access abstraction
void _registerRepositories() {
  // Auth repository
  Get.lazyPut<AuthRepository>(
      () => AuthRepositoryImpl(
            firebaseService: Get.find<FirebaseService>(),
          ),
      fenix: true);

  // User repository
  Get.lazyPut<UserRepository>(
      () => UserRepositoryImpl(
            firebaseService: Get.find<FirebaseService>(),
          ),
      fenix: true);

  // Match repository
  Get.lazyPut<MatchRepository>(
      () => MatchRepositoryImpl(
            firebaseService: Get.find<FirebaseService>(),
          ),
      fenix: true);

  // Interests repository
  Get.lazyPut<InterestsRepository>(
      () => InterestsRepositoryImpl(
            firebaseService: Get.find<FirebaseService>(),
          ),
      fenix: true);
}

/// Register all controllers
///
/// Some controllers are registered as permanent singletons
/// Others are registered as lazy singletons that persist (fenix: true)
void _registerControllers() {
  // App controller (permanent singleton)
  Get.put<AppController>(
      AppController(
        storageService: Get.find<StorageService>(),
      ),
      permanent: true);

  // Core controllers (lazy singletons that persist)
  Get.lazyPut<LanguageController>(
      () => LanguageController(
            storageService: Get.find<StorageService>(),
          ),
      fenix: true);

  Get.lazyPut<UserController>(
      () => UserController(
            userRepository: Get.find<UserRepository>(),
            appController: Get.find<AppController>(),
            storageService: Get.find<StorageService>(),
          ),
      fenix: true);

  // New split user controllers
  Get.lazyPut<UserDataController>(
      () => UserDataController(
            userRepository: Get.find<UserRepository>(),
            appController: Get.find<AppController>(),
            storageService: Get.find<StorageService>(),
          ),
      fenix: true);

  Get.lazyPut<UserFormController>(
      () => UserFormController(
            userDataController: Get.find<UserDataController>(),
          ),
      fenix: true);

  Get.lazyPut<UserSessionController>(
      () => UserSessionController(
            userRepository: Get.find<UserRepository>(),
            appController: Get.find<AppController>(),
            userDataController: Get.find<UserDataController>(),
            userFormController: Get.find<UserFormController>(),
            authNavigationService: Get.find<AuthNavigationService>(),
          ),
      fenix: true);

  // Note: These controllers are registered here for convenience,
  // but they should be accessed through their respective bindings
  // when navigating to screens that use them.

  // Auth controllers
  Get.lazyPut<MobileLoginController>(
      () => MobileLoginController(
            userRepository: Get.find<UserRepository>(),
            appController: Get.find<AppController>(),
            authNavigationService: Get.find<AuthNavigationService>(),
          ),
      fenix: true);

  Get.lazyPut<SaveRegisterDataController>(
      () => SaveRegisterDataController(
            firebaseService: Get.find<FirebaseService>(),
            authRepository: Get.find<AuthRepository>(),
            userRepository: Get.find<UserRepository>(),
            appController: Get.find<AppController>(),
            storageService: Get.find<StorageService>(),
            authNavigationService: Get.find<AuthNavigationService>(),
          ),
      fenix: true);

  // Feature controllers
  Get.lazyPut<InterestsController>(
      () => InterestsController(
            interestsRepository: Get.find<InterestsRepository>(),
            userRepository: Get.find<UserRepository>(),
            appController: Get.find<AppController>(),
            userController: Get.find<UserController>(),
          ),
      fenix: true);

  Get.lazyPut<LocationController>(
      () => LocationController(
            userRepository: Get.find<UserRepository>(),
            locationService: Get.find<LocationService>(),
            appController: Get.find<AppController>(),
            userController: Get.find<UserController>(),
          ),
      fenix: true);

  Get.lazyPut<MatchController>(
      () => MatchController(
            matchRepository: Get.find<MatchRepository>(),
            interestsRepository: Get.find<InterestsRepository>(),
            userController: Get.find<UserController>(),
          ),
      fenix: true);
}
