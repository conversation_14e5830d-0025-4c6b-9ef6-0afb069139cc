/// Firebase Configuration Options
///
/// This file contains platform-specific Firebase configuration options
/// for initializing Firebase services in the application.
library firebase_options;

import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart' show defaultTargetPlatform, TargetPlatform;

/// Default Firebase Options
///
/// Provides platform-specific Firebase configuration options
/// based on the current device platform (Android or iOS)
class DefaultFirebaseOptions {
  /// Returns the appropriate Firebase options for the current platform
  ///
  /// Automatically detects the platform and returns the corresponding
  /// Firebase configuration options
  ///
  /// @throws UnsupportedError if the platform is not supported
  static FirebaseOptions get currentPlatform {
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }


  /// Firebase configuration options for Android platform
  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBpl4kjA2fQZwIS1FykXNBWmNl7k1-_3AQ',
    appId: '1:881282683534:android:9cc9f12b89fc9fd8636fdf',
    messagingSenderId: '881282683534',
    projectId: 'towasl',
    storageBucket: 'towasl.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBlI5cDP5Ng2Zh5IIjC1_OJXIx6HLfQ1Ps',
    appId: '1:881282683534:android:9cc9f12b89fc9fd8636fdf',
    messagingSenderId: '881282683534',
    projectId: 'towasl',
    storageBucket: 'towasl.appspot.com',
    iosClientId: '881282683534-pflup69v74rfjj1a2s3f1664s8al0o4v.apps.googleusercontent.com',
    iosBundleId: 'com.app.towasl',
  );
}
