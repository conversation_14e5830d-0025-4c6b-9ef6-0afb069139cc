import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Language Controller
/// Manages application language settings and localization
/// Handles switching between English and Arabic
class LanguageController extends GetxController {
  // Services
  final StorageService _storageService;

  // Current language code (en or ar)
  var currentLang = "en".obs;

  /// Constructor with dependency injection
  ///
  /// @param storageService The storage service for persistent data
  LanguageController({
    required StorageService storageService,
  }) : _storageService = storageService;

  @override
  void onInit() {
    // Load saved language preference on initialization
    getLanguage();
    super.onInit();
  }

  /// Changes the application language
  ///
  /// Updates the app locale and saves the preference
  ///
  /// @param langCode Language code to switch to (en or ar)
  changeLanguage(String langCode) async {
    // Create locale with appropriate country code
    var locale = Locale(langCode, langCode == "en" ? "US" : "SA");

    // Update GetX locale
    Get.updateLocale(locale);

    // Update controller state
    currentLang.value = langCode;

    // Save preference to persistent storage
    _storageService.setLanguage(langCode);
  }

  /// Retrieves saved language preference
  ///
  /// Loads the language setting from shared preferences
  /// and applies it to the application
  getLanguage() {
    // Get saved language code
    currentLang.value = _storageService.getLanguage();

    // Apply the language setting
    changeLanguage(currentLang.value);
  }
}
