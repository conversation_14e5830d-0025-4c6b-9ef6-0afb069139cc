/// Location Binding
///
/// Provides dependency injection for location-related controllers
/// Used with GetX navigation to ensure controllers are available
library location_binding;

import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/location_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/location_service.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for location-related controllers
///
/// Registers controllers needed for the location screen
/// Ensures controllers are properly instantiated when needed
class LocationBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure required services are available
    if (!Get.isRegistered<FirebaseService>()) {
      throw Exception('FirebaseService must be registered before LocationBinding');
    }

    if (!Get.isRegistered<LocationService>()) {
      throw Exception('LocationService must be registered before LocationBinding');
    }

    if (!Get.isRegistered<StorageService>()) {
      throw Exception('StorageService must be registered before LocationBinding');
    }

    if (!Get.isRegistered<AppController>()) {
      throw Exception('AppController must be registered before LocationBinding');
    }

    // Register user controller for user profile operations
    Get.lazyPut<UserController>(() => UserController(
      firebaseService: Get.find<FirebaseService>(),
      appController: Get.find<AppController>(),
      storageService: Get.find<StorageService>(),
    ));

    // Register location controller for managing user location
    Get.lazyPut<LocationController>(() => LocationController(
      firebaseService: Get.find<FirebaseService>(),
      locationService: Get.find<LocationService>(),
      appController: Get.find<AppController>(),
    ));
  }
}
