/// Mobile OTP Binding
///
/// Provides dependency injection for the Mobile OTP verification page
/// Registers the OTP verification controller
library mobile_otp_binding;

import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/auth_controllers/otp_verification_controller.dart';

/// Binding for the Mobile OTP verification page
class MobileOtpBinding extends Bindings {
  /// Mobile number to which OTP was sent
  final String mobileNumber;
  
  /// User ID for the account being verified
  final String userId;
  
  /// Constructor
  MobileOtpBinding({
    required this.mobileNumber,
    required this.userId,
  });
  
  @override
  void dependencies() {
    // Get the app controller
    final appController = Get.find<AppController>();
    
    // Register the OTP verification controller
    Get.put<OtpVerificationController>(
      OtpVerificationController(
        mobileNumber: mobileNumber,
        userId: userId,
        appController: appController,
      ),
    );
  }
}
