/// Home Binding
///
/// Provides dependency injection for home-related controllers
/// Used with GetX navigation to ensure controllers are available
library home_binding;

import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/match_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/interests_repository.dart';
import 'package:towasl/core/repositories/match_repository.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for home-related controllers
///
/// Registers controllers needed for the home screen
/// Ensures controllers are properly instantiated when needed
class HomeBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure required services are available
    if (!Get.isRegistered<FirebaseService>()) {
      throw Exception('FirebaseService must be registered before HomeBinding');
    }

    if (!Get.isRegistered<StorageService>()) {
      throw Exception('StorageService must be registered before HomeBinding');
    }

    if (!Get.isRegistered<AppController>()) {
      throw Exception('AppController must be registered before HomeBinding');
    }

    // Register user controller for user profile operations
    Get.lazyPut<UserController>(() => UserController(
          userRepository: Get.find<UserRepository>(),
          appController: Get.find<AppController>(),
          storageService: Get.find<StorageService>(),
        ));

    // Register match controller for user matching operations
    Get.lazyPut<MatchController>(() => MatchController(
          matchRepository: Get.find<MatchRepository>(),
          interestsRepository: Get.find<InterestsRepository>(),
          userController: Get.find<UserController>(),
        ));
  }
}
