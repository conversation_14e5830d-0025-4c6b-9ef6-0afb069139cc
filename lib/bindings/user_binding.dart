/// User Binding
///
/// Provides dependency injection for user-related controllers
/// Used with GetX navigation to ensure controllers are available
library user_binding;

import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user/user_data_controller.dart';
import 'package:towasl/controllers/user/user_form_controller.dart';
import 'package:towasl/controllers/user/user_session_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/auth_navigation_service.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for user-related controllers
///
/// Registers the split user controllers with proper dependency injection
/// Ensures controllers are properly instantiated when needed
class UserBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure required services are available
    if (!Get.isRegistered<UserRepository>()) {
      throw Exception('UserRepository must be registered before UserBinding');
    }

    if (!Get.isRegistered<StorageService>()) {
      throw Exception('StorageService must be registered before UserBinding');
    }

    if (!Get.isRegistered<AppController>()) {
      throw Exception('AppController must be registered before UserBinding');
    }

    // Register user data controller first (base dependency)
    Get.put<UserDataController>(UserDataController(
      userRepository: Get.find<UserRepository>(),
      appController: Get.find<AppController>(),
      storageService: Get.find<StorageService>(),
    ));

    // Register user form controller (depends on UserDataController)
    Get.put<UserFormController>(UserFormController(
      userDataController: Get.find<UserDataController>(),
    ));

    // Register user session controller (depends on both previous controllers)
    Get.put<UserSessionController>(UserSessionController(
      userRepository: Get.find<UserRepository>(),
      appController: Get.find<AppController>(),
      userDataController: Get.find<UserDataController>(),
      userFormController: Get.find<UserFormController>(),
      authNavigationService: Get.find<AuthNavigationService>(),
    ));
  }
}
