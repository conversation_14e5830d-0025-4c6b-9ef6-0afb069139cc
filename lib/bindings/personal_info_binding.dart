/// Personal Info Binding
///
/// Provides dependency injection for personal info-related controllers
/// Used with GetX navigation to ensure controllers are available
library personal_info_binding;

import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/location_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/location_service.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for personal info-related controllers
///
/// Registers controllers needed for the personal info screen
/// Ensures controllers are properly instantiated when needed
class PersonalInfoBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure required services are available
    if (!Get.isRegistered<FirebaseService>()) {
      throw Exception(
          'FirebaseService must be registered before PersonalInfoBinding');
    }

    if (!Get.isRegistered<LocationService>()) {
      throw Exception(
          'LocationService must be registered before PersonalInfoBinding');
    }

    if (!Get.isRegistered<StorageService>()) {
      throw Exception(
          'StorageService must be registered before PersonalInfoBinding');
    }

    if (!Get.isRegistered<AppController>()) {
      throw Exception(
          'AppController must be registered before PersonalInfoBinding');
    }

    // Register user controller for user profile operations
    Get.lazyPut<UserController>(() => UserController(
          userRepository: Get.find<UserRepository>(),
          appController: Get.find<AppController>(),
          storageService: Get.find<StorageService>(),
        ));

    // Register location controller for location operations
    Get.lazyPut<LocationController>(() => LocationController(
          userRepository: Get.find<UserRepository>(),
          locationService: Get.find<LocationService>(),
          appController: Get.find<AppController>(),
          userController: Get.find<UserController>(),
        ));
  }
}
