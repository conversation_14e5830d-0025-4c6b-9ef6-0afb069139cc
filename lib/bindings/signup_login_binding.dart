/// Login Binding
///
/// Provides dependency injection for login-related controllers
/// Used with GetX navigation to ensure controllers are available
library signup_login_binding;

import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/auth_controllers/mobile_login_controller.dart';
import 'package:towasl/controllers/auth_controllers/save_register_data_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for login-related controllers
///
/// Registers controllers needed for the login flow
/// Ensures controllers are lazily instantiated when needed
class SignupLoginBinding extends Bindings {
  @override
  void dependencies() {
    // Ensure required services are available
    if (!Get.isRegistered<FirebaseService>()) {
      throw Exception(
          'FirebaseService must be registered before SignupLoginBinding');
    }

    if (!Get.isRegistered<StorageService>()) {
      throw Exception(
          'StorageService must be registered before SignupLoginBinding');
    }

    if (!Get.isRegistered<AppController>()) {
      throw Exception(
          'AppController must be registered before SignupLoginBinding');
    }

    // Register mobile login controller for handling mobile authentication
    Get.lazyPut<MobileLoginController>(() => MobileLoginController(
          userRepository: Get.find<UserRepository>(),
          appController: Get.find<AppController>(),
        ));

    // Register data controller for saving user information after authentication
    Get.lazyPut<SaveRegisterDataController>(() => SaveRegisterDataController(
          firebaseService: Get.find<FirebaseService>(),
          userRepository: Get.find<UserRepository>(),
          appController: Get.find<AppController>(),
          storageService: Get.find<StorageService>(),
        ));
  }
}
