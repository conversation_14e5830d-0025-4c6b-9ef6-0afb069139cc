/// App Binding
///
/// Provides dependency injection for app-level controllers
/// Used with GetX navigation to ensure controllers are available
library app_binding;

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for app-level controllers
///
/// Registers controllers needed for global state management
/// Ensures controllers are properly instantiated when needed
class AppBinding extends Bindings {
  @override
  void dependencies() {
    // Register services first
    if (!Get.isRegistered<StorageService>()) {
      Get.lazyPut<StorageService>(() => StorageServiceImpl(GetStorage()), fenix: true);
    }

    // Register app controller as a permanent singleton
    Get.put<AppController>(
      AppController(
        storageService: Get.find<StorageService>(),
      ),
      permanent: true,
    );
  }
}
