/// Interests Binding
///
/// Provides dependency injection for interests-related controllers
/// Used with GetX navigation to ensure controllers are available
library interests_binding;

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/interests_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/interests_repository.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/storage_service.dart';

/// Binding for interests-related controllers
///
/// Registers controllers needed for the interests screen
/// Ensures controllers are properly instantiated when needed
class InterestsBinding extends Bindings {
  @override
  void dependencies() {
    if (kDebugMode) {
      print("=== InterestsBinding.dependencies() called ===");
    }

    // Ensure required services are available
    if (!Get.isRegistered<FirebaseService>()) {
      throw Exception(
          'FirebaseService must be registered before InterestsBinding');
    }

    if (!Get.isRegistered<StorageService>()) {
      throw Exception(
          'StorageService must be registered before InterestsBinding');
    }

    if (!Get.isRegistered<AppController>()) {
      throw Exception(
          'AppController must be registered before InterestsBinding');
    }

    if (kDebugMode) {
      print("All required services are registered");
    }

    // Register user controller for user profile operations first
    if (kDebugMode) {
      print("Creating UserController...");
    }
    Get.put<UserController>(UserController(
      userRepository: Get.find<UserRepository>(),
      appController: Get.find<AppController>(),
      storageService: Get.find<StorageService>(),
    ));

    // Register interests controller for managing user interests
    if (kDebugMode) {
      print("Creating InterestsController...");
    }
    Get.put<InterestsController>(InterestsController(
      interestsRepository: Get.find<InterestsRepository>(),
      userRepository: Get.find<UserRepository>(),
      appController: Get.find<AppController>(),
      userController: Get.find<UserController>(),
    ));

    if (kDebugMode) {
      print("InterestsBinding.dependencies() completed");
    }
  }
}
