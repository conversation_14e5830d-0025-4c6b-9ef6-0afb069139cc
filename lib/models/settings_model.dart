/// Settings Model
///
/// Represents application settings and configuration
/// Used for storing API credentials and service configuration
///
/// This model is primarily used for SMS service configuration
library settings_model;

/// Settings Model Class
///
/// Contains API credentials and configuration for external services
/// All fields are required to ensure proper service functionality
class SettingsModel {
  /// API key for authentication with external services
  final String apiKey;

  /// User sender ID for SMS services
  final String userSender;

  /// Username for service authentication
  final String userName;

  /// Provider name for identifying the service
  final String providerName;

  /// Default constructor with required parameters
  ///
  /// Creates a new SettingsModel instance with the provided values
  /// All parameters are required for proper service functionality
  SettingsModel({
    required this.apiKey,
    required this.userSender,
    required this.userName,
    required this.providerName,
  });

  /// Factory constructor to create a SettingsModel from Firestore data
  ///
  /// Parses a map from Firestore and creates a SettingsModel instance
  /// Provides empty string fallbacks for missing data
  ///
  /// @param data Map containing settings data from Firestore
  /// @return SettingsModel instance with data from the map
  factory SettingsModel.fromMap(Map<String, dynamic> data) {
    return SettingsModel(
      apiKey: data["apiKey"] ?? '',
      userSender: data["userSender"] ?? '',
      userName: data["userName"] ?? '',
      providerName: data["provider_name"] ?? '',
    );
  }

  /// Converts the SettingsModel to a map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing all settings data in the format expected by Firestore
  Map<String, dynamic> toMap() {
    return {
      "apiKey": apiKey,
      "userSender": userSender,
      "userName": userName,
      "provider_name": providerName,
    };
  }
}
