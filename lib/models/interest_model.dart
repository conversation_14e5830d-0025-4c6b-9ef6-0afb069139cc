/// Interest Model
///
/// Represents interest categories and subcategories in the application
/// Used for displaying and selecting user interests
///
/// This model includes categories (e.g., "Sports") and subcategories (e.g., "Football", "Basketball")
library interest_model;

import 'dart:convert';

/// Converts a JSON string to a list of InterestModel objects
///
/// @param str JSON string representation of interest categories
/// @return List of InterestModel instances created from the JSON data
List<InterestModel> interestModelFromJson(String str) =>
    List<InterestModel>.from(
        json.decode(str).map((x) => InterestModel.fromJson(x)));

/// Interest Category Model
///
/// Represents a category of interests with its subcategories
/// Used for organizing and displaying interest options
class InterestModel {
  /// List of subcategories within this interest category
  List<SubCategories>? subCategories;

  /// Whether this category is currently shown/expanded in the UI
  bool? isShown;

  /// Order number for sorting categories
  String? orderNumber;

  /// Category name (e.g., "Sports", "Music", "Food")
  String? category;

  /// Unique identifier for the category
  String? id;

  /// Default constructor with optional parameters
  ///
  /// Creates a new InterestModel instance with the provided values
  /// All parameters are optional to support partial data
  InterestModel({
    this.subCategories,
    this.isShown,
    this.orderNumber,
    this.category,
    this.id,
  });

  /// Factory constructor to create an InterestModel from JSON
  ///
  /// Parses a JSON map and creates an InterestModel instance
  /// Handles null values and type conversions
  ///
  /// @param json Map containing interest category data from Firestore
  /// @return InterestModel instance with data from the JSON map
  factory InterestModel.fromJson(Map<String, dynamic> json) => InterestModel(
        // Parse subcategories array
        subCategories: json["sub_categories"] == null
            ? []
            : List<SubCategories>.from(
                json["sub_categories"]!.map((x) => SubCategories.fromJson(x))),

        // Basic category information
        isShown: json["is_shown"],
        orderNumber: json["order_number"],
        category: json["category"],
        id: json["id"],
      );

  /// Convert InterestModel to JSON
  ///
  /// Converts the InterestModel instance to a JSON map
  /// Used for serialization and debugging
  ///
  /// @return Map containing the interest data
  Map<String, dynamic> toJson() => {
        "sub_categories": subCategories?.map((x) => x.toJson()).toList(),
        "is_shown": isShown,
        "order_number": orderNumber,
        "category": category,
        "id": id,
      };
}

/// Interest Subcategory Model
///
/// Represents a specific interest within a category
/// Includes selection state for user interaction
class SubCategories {
  /// Name of the subcategory (e.g., "Football", "Jazz", "Italian Food")
  String? subcategory;

  /// Whether this subcategory is selected by the user
  bool? isSelected;

  /// Default constructor with optional parameters
  SubCategories({
    this.subcategory,
    this.isSelected,
  });

  /// Factory constructor to create a SubCategories from JSON
  ///
  /// Parses a string and creates a SubCategories instance
  /// Always initializes with isSelected = false
  ///
  /// @param json String containing subcategory name
  /// @return SubCategories instance with the name and default selection state
  factory SubCategories.fromJson(String json) {
    return SubCategories(subcategory: json.toString(), isSelected: false);
  }

  /// Convert SubCategories to JSON
  ///
  /// Converts the SubCategories instance to a JSON string
  /// Used for serialization and debugging
  ///
  /// @return String containing the subcategory name
  String toJson() => subcategory ?? '';
}
