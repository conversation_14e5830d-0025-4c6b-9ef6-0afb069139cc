/// User Model
///
/// Represents a user in the application with all their profile information
/// Used for storing and retrieving user data from Firestore
///
/// This model includes personal information, location, and interests
library user_model;

import 'dart:convert';

/// Converts a JSON string to a UserModel object
///
/// @param str JSON string representation of a user
/// @return UserModel instance created from the JSON data
UserModel userModelFromJson(String str) => UserModel.fromJson(json.decode(str));

/// Converts a UserModel object to a JSON string
///
/// @param data UserModel instance to convert
/// @return JSON string representation of the user
String userModelToJson(UserModel data) => json.encode(data.toJson());

/// User Model Class
///
/// Contains all user profile information and preferences
/// All fields are nullable to support partial profile completion
class UserModel {
  /// User's gender (e.g., "male", "female")
  String? gender;

  /// User's nationality
  String? nationality;

  /// Unique user identifier
  String? userId;

  /// User's mobile number (primary identifier)
  String? mobile;

  /// User's display name
  String? name;

  /// User's phone number
  String? phoneNumber;

  /// Country code for phone number (e.g., "966" for Saudi Arabia)
  String? countryCode;

  /// User's location information (city, country, coordinates)
  UserLocation? userLocation;

  /// User's birth year (used for age calculation)
  String? birthdayYear;

  /// User's interests organized by categories
  /// Map where keys are interest categories and values are lists of specific interests
  Map<String, List<String>>? userInterest;

  /// Default constructor with optional parameters
  ///
  /// Creates a new UserModel instance with the provided values
  /// All parameters are optional to support partial profile creation
  UserModel(
      {this.gender,
      this.mobile,
      this.nationality,
      this.userId,
      this.name,
      this.phoneNumber,
      this.userLocation,
      this.birthdayYear,
      this.userInterest,
      this.countryCode});

  /// Factory constructor to create a UserModel from JSON
  ///
  /// Parses a JSON map and creates a UserModel instance
  /// Handles null values and type conversions
  ///
  /// @param json Map containing user data from Firestore
  /// @return UserModel instance with data from the JSON map
  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        // Basic user information with empty string fallbacks
        mobile: json["mobile"] ?? json["email"] ?? '', // Support both mobile and legacy email field
        gender: json["gender"] ?? '',
        nationality: json["nationality"] ?? '',
        userId: json["user_id"],
        name: json["name"] ?? '',
        phoneNumber: json["phone_number"] ?? "",
        countryCode: json["countryCode"] ?? "",

        // Parse nested UserLocation object if present
        userLocation: json["user_location"] == null
            ? null
            : UserLocation.fromJson(json["user_location"]),

        birthdayYear: json["birthdayYear"] ?? '',

        // Parse nested interests map if present
        // Converts from dynamic types to strongly typed Map<String, List<String>>
        userInterest: json["user_interest"] == null
            ? null
            : Map.from(json["user_interest"]!).map((k, v) =>
                MapEntry<String, List<String>>(
                    k, List<String>.from(v.map((x) => x)))),
      );

  /// Converts the UserModel to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing all user data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        // Basic user information
        "mobile": mobile,
        "gender": gender,
        "nationality": nationality,
        "user_id": userId,
        "name": name,
        "phone_number": phoneNumber,

        // Convert nested objects to JSON
        "user_location": userLocation?.toJson(),
        "birthdayYear": birthdayYear,

        // Convert interests map to the format expected by Firestore
        "user_interest": userInterest == null
            ? null
            : Map.from(userInterest!).map((k, v) =>
                MapEntry<String, dynamic>(k, List<dynamic>.from(v.map((x) => x)))),
      };
}

/// User Location Class
///
/// Contains geographical information about a user's location
/// Includes both coordinates and address components
class UserLocation {
  /// Country name
  String? country;

  /// Longitude coordinate
  double? lng;

  /// City name
  String? city;

  /// Latitude coordinate
  double? lat;

  /// District or neighborhood name
  String? district;

  /// Default constructor with optional parameters
  ///
  /// Creates a new UserLocation instance with the provided values
  /// All parameters are optional to support partial location data
  UserLocation({
    this.country,
    this.lng,
    this.city,
    this.lat,
    this.district,
  });

  /// Factory constructor to create a UserLocation from JSON
  ///
  /// Parses a JSON map and creates a UserLocation instance
  /// Handles type conversions for coordinates
  ///
  /// @param json Map containing location data from Firestore
  /// @return UserLocation instance with data from the JSON map
  factory UserLocation.fromJson(Map<String, dynamic> json) => UserLocation(
        country: json["country"],
        lng: json["lng"]?.toDouble(),
        city: json["city"],
        lat: json["lat"]?.toDouble(),
        district: json["district"],
      );

  /// Converts the UserLocation to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing all location data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        "country": country,
        "lng": lng,
        "city": city,
        "lat": lat,
        "district": district,
      };
}
