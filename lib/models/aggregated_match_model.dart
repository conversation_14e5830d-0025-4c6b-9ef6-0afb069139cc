/// Aggregated Match Model
///
/// Represents a potential match for a user with additional matching information
/// Used for displaying and filtering potential matches based on compatibility
///
/// This model extends user information with matching-specific data like similarity percentage
library aggregated_match_model;

import 'dart:convert';
import 'package:flutter/foundation.dart';

/// Converts a JSON string to a list of AggregatedMatchModel objects
///
/// @param str JSON string representation of potential matches
/// @return List of AggregatedMatchModel instances created from the JSON data
List<AggregatedMatchModel> aggregatedMatchModelFromJson(String str) =>
    List<AggregatedMatchModel>.from(
        json.decode(str).map((x) => AggregatedMatchModel.fromJson(x)));

/// Converts a list of AggregatedMatchModel objects to a JSON string
///
/// @param data List of AggregatedMatchModel instances to convert
/// @return JSON string representation of the matches
String aggregatedMatchModelToJson(List<AggregatedMatchModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.to<PERSON>son())));

/// Aggregated Match Model Class
///
/// Contains user information with additional matching-related data
/// Used for displaying potential matches and their compatibility
class AggregatedMatchModel {
  /// User's gender (e.g., "male", "female")
  String? gender;

  /// User's nationality
  String? nationality;

  /// Unique user identifier
  String? userId;

  /// Country code for phone number (e.g., "966" for Saudi Arabia)
  String? countryCode;

  /// User's display name
  String? name;

  /// User's phone number
  String? phoneNumber;

  /// User's location information (city, country, coordinates)
  UserLocation? userLocation;

  /// User's birth year (used for age calculation)
  String? birthdayYear;

  /// User's interests organized by categories
  Map<String, List<String>>? userInterest;

  /// Selected interests for matching purposes
  UserSelectedInterest? userSelectedInterest;

  /// Compatibility percentage with the current user (0-100)
  String? similarPercentage;
  /// Default constructor with optional parameters
  ///
  /// Creates a new AggregatedMatchModel instance with the provided values
  /// All parameters are optional to support partial data
  AggregatedMatchModel(
      {this.userSelectedInterest,
      this.gender,
      this.nationality,
      this.userId,
      this.countryCode,
      this.name,
      this.phoneNumber,
      this.userLocation,
      this.birthdayYear,
      this.userInterest,
      this.similarPercentage});

  /// Factory constructor to create an AggregatedMatchModel from JSON
  ///
  /// Parses a JSON map and creates an AggregatedMatchModel instance
  /// Handles null values and type conversions
  ///
  /// @param json Map containing match data from Firestore
  /// @return AggregatedMatchModel instance with data from the JSON map
  factory AggregatedMatchModel.fromJson(Map<String, dynamic> json) =>
      AggregatedMatchModel(
        // Initialize with default similarity percentage
        similarPercentage: "0",

        // Basic user information
        gender: json["gender"],
        nationality: json["nationality"],
        userId: json["user_id"],
        countryCode: json["countryCode"],
        name: json["name"],
        phoneNumber: json["phone_number"],

        // Parse nested UserLocation object if present
        userLocation: json["user_location"] == null
            ? null
            : UserLocation.fromJson(json["user_location"]),

        birthdayYear: json["birthdayYear"],

        // Parse nested interests map if present
        userInterest: Map.from(json["user_interest"]!).map((k, v) =>
            MapEntry<String, List<String>>(
                k, List<String>.from(v.map((x) => x)))),
      );

  /// Converts the AggregatedMatchModel to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing all match data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        // Basic user information
        "gender": gender,
        "nationality": nationality,
        "user_id": userId,
        "countryCode": countryCode,
        "name": name,
        "phone_number": phoneNumber,

        // Convert nested objects to JSON
        "user_location": userLocation?.toJson(),
        "birthdayYear": birthdayYear,

        // Convert interests map to the format expected by Firestore
        "user_interest": userInterest == null
            ? null
            : Map.from(userInterest!).map((k, v) =>
                MapEntry<String, dynamic>(k, List<dynamic>.from(v.map((x) => x)))),
      };
}

/// User Location Class
///
/// Contains geographical information about a user's location
/// Includes both coordinates and address components
class UserLocation {
  /// Country name
  String? country;

  /// Longitude coordinate
  double? lng;

  /// City name
  String? city;

  /// Latitude coordinate
  double? lat;

  /// District or neighborhood name
  String? district;

  /// Default constructor with optional parameters
  ///
  /// Creates a new UserLocation instance with the provided values
  /// All parameters are optional to support partial location data
  UserLocation({
    this.country,
    this.lng,
    this.city,
    this.lat,
    this.district,
  });

  /// Factory constructor to create a UserLocation from JSON
  ///
  /// Parses a JSON map and creates a UserLocation instance
  /// Handles type conversions for coordinates
  ///
  /// @param json Map containing location data from Firestore
  /// @return UserLocation instance with data from the JSON map
  factory UserLocation.fromJson(Map<String, dynamic> json) => UserLocation(
        country: json["country"],
        lng: json["lng"]?.toDouble(),
        city: json["city"],
        lat: json["lat"]?.toDouble(),
        district: json["district"],
      );

  /// Converts the UserLocation to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  ///
  /// @return Map containing all location data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        "country": country,
        "lng": lng,
        "city": city,
        "lat": lat,
        "district": district,
      };
}

/// User Selected Interest Class
///
/// Contains a specific interest category and its selected items
/// Used for matching algorithms and displaying user preferences
class UserSelectedInterest {
  /// Interest category name (e.g., "Sports", "Music")
  String? category;

  /// List of selected interests within this category
  List<String>? data;

  /// Default constructor with optional parameters
  UserSelectedInterest({
    this.category,
    this.data,
  });
  /// Factory constructor to create a UserSelectedInterest from JSON
  ///
  /// Parses a map and creates a UserSelectedInterest instance
  /// Currently returns a placeholder instance with default values
  ///
  /// @param dd Map containing interest data
  /// @return UserSelectedInterest instance with default values
  factory UserSelectedInterest.fromJson(Map dd) {
    if (kDebugMode) {
      print(dd);
    }
    return UserSelectedInterest(
      category: 'key',
      data: [],
    );
  }

  /// Converts the UserSelectedInterest to a JSON map
  ///
  /// Creates a map representation suitable for storing in Firestore
  /// Note: The key names don't match the property names (likely a bug)
  ///
  /// @return Map containing interest data in the format expected by Firestore
  Map<String, dynamic> toJson() => {
        "country": category,  // Note: This should probably be "category"
        "lng": data,          // Note: This should probably be "data"
      };
}
