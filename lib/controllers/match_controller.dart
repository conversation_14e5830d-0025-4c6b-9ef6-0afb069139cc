import 'dart:convert';
import 'dart:math';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/interests_repository.dart';
import 'package:towasl/core/repositories/match_repository.dart';
import 'package:towasl/models/aggregated_match_model.dart';
import 'package:towasl/models/user_model.dart';

/// Match Controller
/// Manages user matching functionality and algorithms
/// Handles fetching potential matches and calculating compatibility scores
class MatchController {
  // Services and Controllers
  final MatchRepository _matchRepository;
  final InterestsRepository _interestsRepository;
  final UserController _userController;

  /// Constructor with dependency injection
  ///
  /// @param matchRepository The match repository for data operations
  /// @param interestsRepository The interests repository for interest data
  /// @param userController The user controller for current user data
  MatchController({
    required MatchRepository matchRepository,
    required InterestsRepository interestsRepository,
    required UserController userController,
  })  : _matchRepository = matchRepository,
        _interestsRepository = interestsRepository,
        _userController = userController;

  // Observable list of potential matches
  RxList<AggregatedMatchModel> matchData = <AggregatedMatchModel>[].obs;

  // Current user's interest list for matching
  List<String> userInterestList = [];

  // Loading state
  var isLoading = true.obs;

  // Pagination variables
  DocumentSnapshot? lastDocument;
  var hasMore = false.obs;
  final int limit = 10;

  /// Creates a Firestore query for finding potential matches
  ///
  /// Builds a query with filters for gender, age range, location, and excludes current user
  ///
  /// @return Firestore Query object configured with appropriate filters
  returnQuery() {
    // Create age range list (current user's age +/- 4 years)
    List birthYearList = [];

    // Add years above user's birth year
    for (int i = 0; i < 4; i++) {
      birthYearList.add(
          (int.parse(_userController.userModel.value.birthdayYear.toString()) +
                  i)
              .toString());
    }

    // Add years below user's birth year
    for (int i = 1; i < 4; i++) {
      birthYearList.add(
          (int.parse(_userController.userModel.value.birthdayYear.toString()) -
                  i)
              .toString());
    }

    // Build and return query with filters using repository
    return _matchRepository.createMatchQuery(
      currentUserId: _userController.userModel.value.userId ?? '',
      gender: _userController.userModel.value.gender.toString(),
      birthYearList: birthYearList.map((e) => e.toString()).toList(),
      city: _userController.userModel.value.userLocation!.city.toString(),
    );
  }

  /// Fetches potential matches from Firestore with pagination
  ///
  /// Retrieves users that match criteria and calculates compatibility scores
  /// Supports pagination for loading more matches
  ///
  /// @param userInterestList2 List of current user's interests
  /// @param val Search value (if any)
  getAllData(List<String> userInterestList2, String val) async {
    isLoading(true);

    try {
      // Create age range list (current user's age +/- 4 years)
      List<String> birthYearList = [];

      // Add years above user's birth year
      for (int i = 1; i <= 4; i++) {
        birthYearList.add((int.parse(
                    _userController.userModel.value.birthdayYear.toString()) +
                i)
            .toString());
      }

      // Add years below user's birth year
      for (int i = 1; i <= 4; i++) {
        birthYearList.add((int.parse(
                    _userController.userModel.value.birthdayYear.toString()) -
                i)
            .toString());
      }

      // Get potential matches using repository
      final snapshot = await _matchRepository.getPotentialMatches(
        currentUserId: _userController.userModel.value.userId ?? '',
        gender: _userController.userModel.value.gender.toString(),
        birthYearList: birthYearList,
        city: _userController.userModel.value.userLocation!.city.toString(),
        limit: limit,
        lastDocument: lastDocument,
      );

      // Handle empty results
      if (snapshot.docs.isEmpty) {
        isLoading(false);
        hasMore(false);
        return;
      }

      // Convert Firestore documents to a list of maps
      List list = [];
      list = snapshot.docs.map((doc) => doc.data()).toList();

      // Handle pagination results differently
      if (hasMore.value) {
        // Add new matches to existing list when paginating
        List<AggregatedMatchModel> matchDataNew =
            aggregatedMatchModelFromJson(jsonEncode(list));
        matchData.addAll(matchDataNew);
      } else {
        // Replace entire list when loading first page
        matchData.assignAll(aggregatedMatchModelFromJson(jsonEncode(list)));
      }

      // Calculate compatibility score for each match
      matchData.assignAll(matchData.map((model) {
        // Calculate similarity percentage
        model.similarPercentage = calculatePercent(
            model, _userController.userModel.value, userInterestList2);
        return model;
      }).toList());

      // Sort matches by compatibility score (highest first)
      matchData.sort((a, b) => int.parse(b.similarPercentage.toString())
          .compareTo(int.parse(a.similarPercentage.toString())));

      // Store last document for pagination
      if (matchData.isNotEmpty) {
        lastDocument = snapshot.docs.last;
      }

      // Debug output
      for (var element in matchData) {
        if (kDebugMode) {
          print(element.similarPercentage);
        }
      }

      // Check if there are more results to load
      hasMore.value = list.length == limit;
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      return [];
    } finally {
      isLoading(false);
    }
  }

  /// Calculates compatibility percentage between current user and potential match
  ///
  /// Considers multiple factors:
  /// - Age similarity (closer ages score higher)
  /// - Nationality matching
  /// - Common interests
  /// - Geographic proximity
  ///
  /// @param aggregatedMatchModel The potential match user
  /// @param userModel The current user
  /// @param userInterestList2 List of current user's interests
  /// @return String representation of compatibility percentage (0-100)
  calculatePercent(AggregatedMatchModel aggregatedMatchModel,
      UserModel userModel, List<String> userInterestList2) {
    // Initialize scoring variables
    int agePercent = 0;
    int matching = 0;
    int matchingPercent = 0;
    int matchingNationalityPercent = 0;
    int distanceScore = 0;

    // Calculate age similarity score
    // Closer ages get higher scores, with a penalty of 20% per year difference
    agePercent = (100 -
            (((int.parse(userModel.birthdayYear.toString()) -
                        int.parse(aggregatedMatchModel.birthdayYear.toString()))
                    .abs()) *
                20))
        .ceil();

    // Calculate nationality matching score
    if (userModel.nationality.toString() == aggregatedMatchModel.nationality) {
      matchingNationalityPercent = 100; // Same nationality
    } else {
      matchingNationalityPercent = 0; // Different nationality
    }

    // Calculate interest matching score
    userModel.userInterest!.forEach((key, value) {
      if (aggregatedMatchModel.userInterest!.containsKey(key)) {
        // Count matching interests within each category
        for (var element in value) {
          if (aggregatedMatchModel.userInterest![key]!.contains(element)) {
            matching++;
          }
        }
      }
    });

    // Convert matching count to percentage based on user's total interests
    matchingPercent = ((matching / userInterestList2.length) * 100).toInt();

    // Calculate distance score based on geographic proximity
    double distance = calculateDistance(
      userModel.userLocation!.lat,
      userModel.userLocation!.lng,
      aggregatedMatchModel.userLocation!.lat,
      aggregatedMatchModel.userLocation!.lng,
    );
    distanceScore = calculateScore(distance, 100);

    // Calculate overall compatibility as average of all factors
    return ((matchingPercent +
                matchingNationalityPercent +
                agePercent +
                distanceScore) /
            4)
        .ceil()
        .toString();
  }

  /// Calculates the distance between two geographic coordinates using the Haversine formula
  ///
  /// @param lat1 Latitude of first location
  /// @param lon1 Longitude of first location
  /// @param lat2 Latitude of second location
  /// @param lon2 Longitude of second location
  /// @return Distance in kilometers
  double calculateDistance(lat1, lon1, lat2, lon2) {
    var p = 0.017453292519943295; // Math.PI / 180
    var c = cos;
    var a = 0.5 -
        c((lat2 - lat1) * p) / 2 +
        c(lat1 * p) * c(lat2 * p) * (1 - c((lon2 - lon1) * p)) / 2;
    return 12742 * asin(sqrt(a)); // 2 * R; R = 6371 km
  }

  /// Converts distance to a score based on maximum allowed distance
  ///
  /// @param distance Actual distance in kilometers
  /// @param maxDistance Maximum distance to consider (beyond this returns 0)
  /// @return Score from 0-100, with closer distances scoring higher
  int calculateScore(double distance, double maxDistance) {
    if (distance >= maxDistance) {
      return 0; // Too far away
    }
    return (((maxDistance - distance) / maxDistance) * 100).toInt();
  }

  /// Fetches match data with additional interest category information
  ///
  /// Retrieves potential matches and enriches them with interest category details
  /// Uses more specific filters than getAllData method
  getmatchData() async {
    // Flatten user interests into a list
    userInterestList = [];
    _userController.userModel.value.userInterest!.forEach((key, value) {
      for (var element in value) {
        userInterestList.add(element);
      }
    });

    // Create age range list (current user's age + 0-2 years)
    List<String> birthYearList = [];
    for (int i = 0; i < 3; i++) {
      birthYearList.add(
          (int.parse(_userController.userModel.value.birthdayYear.toString()) +
                  i)
              .toString());
    }

    // Get matches with filters using repository
    final matchList = await _matchRepository.getMatchesWithFilters(
      currentUserId: _userController.userModel.value.userId ?? '',
      gender: _userController.userModel.value.gender.toString(),
      birthYearList: birthYearList,
      nationality: _userController.userModel.value.nationality.toString(),
      city: _userController.userModel.value.userLocation!.city.toString(),
    );

    if (matchList.isNotEmpty) {
      // Process results
      debugPrint(jsonEncode(matchList), wrapWidth: 1024);
      matchData.assignAll(aggregatedMatchModelFromJson(jsonEncode(matchList)));

      // Enrich each match with interest category information
      for (int i = 0; i < matchData.length; i++) {
        matchData[i].userInterest!.forEach((key, value) async {
          // Fetch interest category details using repository
          final categoryData = await _interestsRepository
              .getInterestCategoryById(key.toString().trim());
          if (categoryData != null) {
            // Create UserSelectedInterest object with category name and selected interests
            matchData[i].userSelectedInterest = UserSelectedInterest(
                category: categoryData['category'], data: value);
            if (kDebugMode) {
              print(matchData[i].userSelectedInterest?.category);
              print(matchData[i].userSelectedInterest?.data);
            }
          }
        });
      }
    } else {
      if (kDebugMode) {
        print('its empty');
      }
    }
  }
}
