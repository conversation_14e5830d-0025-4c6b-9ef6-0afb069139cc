import 'package:get/get.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/models/user_model.dart';

/// App Controller
/// Manages global application state and configuration
/// Centralizes access to user ID and other global variables
class AppController extends BaseController {
  // Services
  final StorageService _storageService;

  // User state
  final RxString _userId = ''.obs;
  final Rx<UserModel> _userModel = UserModel().obs;

  // API credentials
  final RxString _msegatApikey = "6c3076fac5b70da2eb1d59bcb1475b8b".obs;
  final RxString _msegatUserSender = "".obs;
  final RxString _msegatUsername = "".obs;

  // Authentication provider
  final RxString _providerName = "".obs;

  /// Constructor with dependency injection
  ///
  /// @param storageService The storage service for persistent data
  AppController({
    required StorageService storageService,
  }) : _storageService = storageService;

  /// Initialize the controller
  ///
  /// Loads user ID from storage and sets up initial state
  @override
  void onInit() {
    super.onInit();
    _loadUserIdFromStorage();
  }

  /// Loads the user ID from storage
  ///
  /// Updates the userId reactive variable with the stored value
  void _loadUserIdFromStorage() {
    final storedUserId = _storageService.getUserIDValue();
    if (storedUserId.isNotEmpty) {
      _userId.value = storedUserId;
    }
  }

  /// Get the current user ID
  String get userId => _userId.value;

  /// Set the current user ID
  ///
  /// Updates both the reactive variable and storage
  ///
  /// @param id The new user ID to set
  set userId(String id) {
    _userId.value = id;
    if (id.isNotEmpty) {
      _storageService.setLoginData(id);
    }
  }

  /// Get the current user model
  UserModel get userModel => _userModel.value;

  /// Set the current user model
  ///
  /// Updates the reactive user model variable
  ///
  /// @param model The new user model to set
  set userModel(UserModel model) {
    _userModel.value = model;
    _userModel.refresh();
  }

  /// Get the Msegat API key
  String get msegatApikey => _msegatApikey.value;

  /// Set the Msegat API key
  ///
  /// @param key The new API key to set
  set msegatApikey(String key) {
    _msegatApikey.value = key;
  }

  /// Get the Msegat user sender
  String get msegatUserSender => _msegatUserSender.value;

  /// Set the Msegat user sender
  ///
  /// @param sender The new user sender to set
  set msegatUserSender(String sender) {
    _msegatUserSender.value = sender;
  }

  /// Get the Msegat username
  String get msegatUsername => _msegatUsername.value;

  /// Set the Msegat username
  ///
  /// @param username The new username to set
  set msegatUsername(String username) {
    _msegatUsername.value = username;
  }

  /// Get the provider name
  String get providerName => _providerName.value;

  /// Set the provider name
  ///
  /// @param name The new provider name to set
  set providerName(String name) {
    _providerName.value = name;
  }

  /// Clear all user data
  ///
  /// Resets the user ID and model to empty values
  void clearUserData() {
    _userId.value = '';
    _userModel.value = UserModel();
    _userModel.refresh();
  }
}
