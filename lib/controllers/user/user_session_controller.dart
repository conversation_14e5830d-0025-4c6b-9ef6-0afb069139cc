import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:towasl/bindings/signup_login_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/controllers/user/user_data_controller.dart';
import 'package:towasl/controllers/user/user_form_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/views/signup_login_page/signup_login_page.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// User Session Controller
/// 
/// Handles authentication and session management operations
/// Manages FCM tokens, user blocking status, and logout operations
class UserSessionController extends BaseController {
  // Services and Controllers
  final UserRepository _userRepository;
  final AppController _appController;
  final UserDataController _userDataController;
  final UserFormController _userFormController;

  /// Constructor with dependency injection
  ///
  /// @param userRepository The user repository for data operations
  /// @param appController The app controller for global state
  /// @param userDataController The user data controller for data operations
  /// @param userFormController The user form controller for form management
  UserSessionController({
    required UserRepository userRepository,
    required AppController appController,
    required UserDataController userDataController,
    required UserFormController userFormController,
  })  : _userRepository = userRepository,
        _appController = appController,
        _userDataController = userDataController,
        _userFormController = userFormController;

  // Session state
  RxBool isNotiEnabled = false.obs;

  /// Set Firebase Cloud Messaging (FCM) token
  /// 
  /// Updates the user's FCM token for push notifications and checks blocked status
  /// 
  /// @param context Build context for showing notifications
  Future<void> setFcmToken(BuildContext context) async {
    try {
      startLoading();

      // Get OneSignal push subscription ID
      final pushSubscriptionId = OneSignal.User.pushSubscription.id;
      
      if (pushSubscriptionId == null) {
        if (kDebugMode) {
          print("No push subscription ID available");
        }
        return;
      }

      // Update FCM token in user document via repository
      await _userRepository.updateFcmToken(_appController.userId, pushSubscriptionId);

      if (kDebugMode) {
        print("FCM token updated successfully");
      }

      // Check if user is blocked after updating token
      // ignore: use_build_context_synchronously
      checkUserBlockedStatus(context);
    } catch (e) {
      if (kDebugMode) print("Error setting FCM token: $e");
    } finally {
      stopLoading();
    }
  }

  /// Check if user is blocked
  /// 
  /// Sets up a real-time listener for user blocked status
  /// If blocked, logs out the user and shows a notification
  /// 
  /// @param context Build context for showing notifications
  void checkUserBlockedStatus(BuildContext context) {
    try {
      // Listen to user blocked status via repository
      _userRepository
          .isUserBlocked(_appController.userId)
          .listen((isBlocked) async {
        // If user is blocked
        if (isBlocked) {
          if (kDebugMode) {
            print("User is blocked, logging out");
          }

          // Perform logout operations
          await logout();

          // Navigate to login page
          Get.offAll(() => const SignupLoginPage(),
              binding: SignupLoginBinding());

          // Show blocked notification
          // ignore: use_build_context_synchronously
          ToastCustom.warningToastCustom("Number blocked".tr, context, true);
        }
      });
    } catch (e) {
      if (kDebugMode) print("Error checking user blocked status: $e");
    }
  }

  /// Logout user
  /// 
  /// Clears all user data and session information
  /// Does not handle navigation - that should be done by the calling code
  Future<void> logout() async {
    try {
      startLoading();

      // Clear app controller state
      _appController.clearUserData();

      // Clear user data controller state
      _userDataController.clearUserData();

      // Clear form controller state
      _userFormController.clearFormData();

      // Reset session state
      isNotiEnabled.value = false;

      if (kDebugMode) {
        print("User logged out successfully");
      }
    } catch (e) {
      if (kDebugMode) print("Error during logout: $e");
    } finally {
      stopLoading();
    }
  }

  /// Delete user account
  /// 
  /// Deletes the user account and performs complete cleanup
  /// Navigates to login page after successful deletion
  Future<void> deleteAccount() async {
    try {
      startLoading();

      // Delete user data via UserDataController
      await _userDataController.deleteUserData();

      // Clear form data
      _userFormController.clearFormData();

      // Reset session state
      isNotiEnabled.value = false;

      if (kDebugMode) {
        print("Account deleted successfully");
      }

      // Navigate to login page
      Get.offAll(() => const SignupLoginPage(), binding: SignupLoginBinding());
    } catch (e) {
      if (kDebugMode) print("Error deleting account: $e");
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Update notification settings
  /// 
  /// Updates the user's notification preferences
  /// 
  /// @param enabled Whether notifications should be enabled
  Future<void> updateNotificationSettings(bool enabled) async {
    try {
      isNotiEnabled.value = enabled;

      // Update notification settings in user document
      await _userRepository.updatePersonalInfo(_appController.userId, {
        'notifications_enabled': enabled,
      });

      if (kDebugMode) {
        print("Notification settings updated: $enabled");
      }
    } catch (e) {
      if (kDebugMode) print("Error updating notification settings: $e");
      // Revert the change on error
      isNotiEnabled.value = !enabled;
      rethrow;
    }
  }

  /// Check if user session is valid
  /// 
  /// Validates the current user session
  /// 
  /// @return True if session is valid, false otherwise
  bool isSessionValid() {
    return _appController.userId.isNotEmpty &&
           _userDataController.userModel.value.userId != null;
  }

  /// Refresh user session
  /// 
  /// Refreshes the user session by reloading user data
  Future<void> refreshSession() async {
    try {
      if (_appController.userId.isNotEmpty) {
        await _userDataController.getUserData();
        _userFormController.initializeFormData();
        
        if (kDebugMode) {
          print("User session refreshed");
        }
      }
    } catch (e) {
      if (kDebugMode) print("Error refreshing session: $e");
    }
  }

  /// Initialize session
  /// 
  /// Initializes the user session with the given user ID
  /// Loads user data and initializes form controllers
  /// 
  /// @param userId The user ID to initialize session for
  Future<void> initializeSession(String userId) async {
    try {
      startLoading();

      // Set user ID in app controller
      _appController.userId = userId;

      // Load user data
      await _userDataController.getUserData();

      // Initialize form data
      _userFormController.initializeFormData();

      if (kDebugMode) {
        print("Session initialized for user: $userId");
      }
    } catch (e) {
      if (kDebugMode) print("Error initializing session: $e");
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Get session info
  /// 
  /// Returns information about the current session
  /// 
  /// @return Map containing session information
  Map<String, dynamic> getSessionInfo() {
    return {
      'userId': _appController.userId,
      'isLoggedIn': isSessionValid(),
      'userName': _userDataController.userModel.value.name ?? '',
      'userMobile': _userDataController.userModel.value.mobile ?? '',
      'notificationsEnabled': isNotiEnabled.value,
      'profileComplete': _userDataController.isProfileComplete(),
    };
  }
}
