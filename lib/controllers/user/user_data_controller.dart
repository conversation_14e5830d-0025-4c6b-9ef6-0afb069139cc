import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/models/user_model.dart';

/// User Data Controller
/// 
/// Handles core user data operations including fetching, updating, and deleting user information
/// Focuses solely on data management without UI concerns
class UserDataController extends BaseController {
  // Services
  final UserRepository _userRepository;
  final AppController _appController;
  final StorageService _storageService;

  /// Constructor with dependency injection
  ///
  /// @param userRepository The user repository for data operations
  /// @param appController The app controller for global state
  /// @param storageService The storage service for persistent data
  UserDataController({
    required UserRepository userRepository,
    required AppController appController,
    required StorageService storageService,
  })  : _userRepository = userRepository,
        _appController = appController,
        _storageService = storageService;

  // State variables
  Rx<UserModel> userModel = UserModel().obs;
  List<String> userInterestList = [];

  /// Fetch user data from Firestore
  /// 
  /// Retrieves the current user's data and updates the user model
  /// Extracts user interests into a flat list for easier access
  Future<void> getUserData() async {
    // Check if user is logged in
    if (_appController.userId.isEmpty) {
      if (kDebugMode) print("No user logged in");
      return;
    }

    // Clear previous data and set loading state
    userInterestList.clear();
    startLoading();

    try {
      // Fetch user data from repository
      final userData = await _userRepository.getUserById(_appController.userId);

      if (userData != null) {
        // Update user model
        userModel.value = userData;

        // Extract all interests into a flat list for easier access
        userInterestList = List<String>.from(
            userModel.value.userInterest?.values.expand((i) => i) ?? []);

        // Update UI and global references
        userModel.refresh();
        _appController.userModel = userModel.value;

        if (kDebugMode) {
          print("User data loaded successfully");
          print("User interests count: ${userInterestList.length}");
        }
      }
    } catch (e) {
      if (kDebugMode) print("Error fetching user data: $e");
    } finally {
      // End loading state
      stopLoading();
    }
  }

  /// Update user profile information
  /// 
  /// Updates specific user data fields in Firestore
  /// Refreshes local user data after successful update
  /// 
  /// @param userInfo Map containing the fields to update
  Future<void> updateUserData(Map<String, dynamic> userInfo) async {
    try {
      startLoading();

      // Update user document via repository
      await _userRepository.updatePersonalInfo(_appController.userId, userInfo);

      // Refresh user data from server
      await getUserData();

      if (kDebugMode) {
        print("User data updated successfully");
      }
    } catch (e) {
      if (kDebugMode) print('Error updating user data: $e');
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Delete user account
  /// 
  /// Removes user data from Firestore and clears local state
  /// Does not handle navigation - that should be done by the calling controller
  Future<void> deleteUserData() async {
    try {
      startLoading();

      // Delete user document via repository
      await _userRepository.deleteUser(_appController.userId);

      // Clear app controller state
      _appController.clearUserData();

      // Clear local storage data
      await _storageService.clearUserData();

      // Reset controller data
      userModel.value = UserModel();
      userInterestList.clear();

      if (kDebugMode) {
        print("User account deleted successfully");
      }
    } catch (e) {
      if (kDebugMode) print("Error deleting account: $e");
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Update user interests
  /// 
  /// Updates the user's selected interests in Firestore
  /// Refreshes local user data after successful update
  /// 
  /// @param interests Map of interest categories and selected subcategories
  Future<void> updateUserInterests(Map<String, dynamic> interests) async {
    try {
      startLoading();

      // Update user interests via repository
      await _userRepository.updateUserInterests(_appController.userId, interests);

      // Refresh user data from server
      await getUserData();

      if (kDebugMode) {
        print("User interests updated successfully");
      }
    } catch (e) {
      if (kDebugMode) print('Error updating user interests: $e');
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Update user location
  /// 
  /// Updates the user's location information in Firestore
  /// Refreshes local user data after successful update
  /// 
  /// @param location Map containing location data (lat, lng, city, country, district)
  Future<void> updateUserLocation(Map<String, dynamic> location) async {
    try {
      startLoading();

      // Update user location via repository
      await _userRepository.updateUserLocation(_appController.userId, location);

      // Refresh user data from server
      await getUserData();

      if (kDebugMode) {
        print("User location updated successfully");
      }
    } catch (e) {
      if (kDebugMode) print('Error updating user location: $e');
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Check if user profile is complete
  /// 
  /// Determines if the user has completed all required profile fields
  /// 
  /// @return True if profile is complete, false otherwise
  bool isProfileComplete() {
    final user = userModel.value;
    
    return user.userInterest != null &&
           user.userLocation != null &&
           (user.nationality ?? '').isNotEmpty &&
           (user.name ?? '').isNotEmpty &&
           (user.gender ?? '').isNotEmpty &&
           (user.birthdayYear ?? '').isNotEmpty;
  }

  /// Get user's age based on birth year
  /// 
  /// Calculates the user's current age from their birth year
  /// 
  /// @return User's age as an integer, or 0 if birth year is not set
  int getUserAge() {
    final birthYear = userModel.value.birthdayYear;
    if (birthYear == null || birthYear.isEmpty) return 0;
    
    try {
      final currentYear = DateTime.now().year;
      return currentYear - int.parse(birthYear);
    } catch (e) {
      if (kDebugMode) print("Error calculating user age: $e");
      return 0;
    }
  }

  /// Clear all user data
  /// 
  /// Resets the user model and interest list to empty state
  /// Used when logging out or switching users
  void clearUserData() {
    userModel.value = UserModel();
    userInterestList.clear();
    userModel.refresh();
  }
}
