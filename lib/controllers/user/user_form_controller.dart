import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/controllers/user/user_data_controller.dart';
import 'package:towasl/models/user_model.dart';
import 'package:towasl/views/home_page/home_page.dart';

/// User Form Controller
/// 
/// Handles form state management and validation for user profile forms
/// Manages TextEditingControllers and form validation logic
class UserFormController extends BaseController {
  // Dependencies
  final UserDataController _userDataController;

  /// Constructor with dependency injection
  ///
  /// @param userDataController The user data controller for data operations
  UserFormController({
    required UserDataController userDataController,
  }) : _userDataController = userDataController;

  // Form controllers for user profile data
  TextEditingController nameController = TextEditingController();
  TextEditingController nationalityController = TextEditingController();
  TextEditingController yearController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  // User gender selection
  String? gender;
  RxBool showGenderValidation = false.obs;
  RxBool showValidationError = false.obs;

  @override
  void onInit() {
    super.onInit();
    gender = null;
    // Ensure validation flags are properly initialized
    showGenderValidation.value = false;
    showValidationError.value = false;
  }

  @override
  void onClose() {
    // Dispose of text controllers to prevent memory leaks
    nameController.dispose();
    nationalityController.dispose();
    yearController.dispose();
    locationController.dispose();
    emailController.dispose();
    super.onClose();
  }

  /// Initialize form controllers with user data
  /// 
  /// Populates text controllers with data from the user model
  /// Should be called after user data is loaded
  void initializeFormData() {
    final userModel = _userDataController.userModel.value;

    // Set gender selection
    gender = userModel.gender;

    // Set text field values from user model
    nameController.text = userModel.name ?? "";
    nationalityController.text = userModel.nationality ?? "";
    yearController.text = userModel.birthdayYear ?? "";

    // Format location as district, city, country
    if (userModel.userLocation != null) {
      locationController.text = "${userModel.userLocation?.district}, "
          "${userModel.userLocation?.city}, "
          "${userModel.userLocation?.country}";
    }

    emailController.text = userModel.mobile ?? "";

    if (kDebugMode) {
      print("Form data initialized from user model");
    }
  }

  /// Validate form data
  /// 
  /// Checks if all required fields are filled and valid
  /// Updates validation state flags
  /// 
  /// @return True if form is valid, false otherwise
  bool validateForm() {
    bool isValid = true;

    // Reset validation flags
    showGenderValidation.value = false;
    showValidationError.value = false;

    // Validate required fields
    if (nameController.text.trim().isEmpty) {
      showValidationError.value = true;
      isValid = false;
    }

    if (nationalityController.text.trim().isEmpty) {
      showValidationError.value = true;
      isValid = false;
    }

    if (yearController.text.trim().isEmpty) {
      showValidationError.value = true;
      isValid = false;
    }

    if (gender == null || gender!.isEmpty) {
      showGenderValidation.value = true;
      isValid = false;
    }

    // Validate birth year format and range
    if (yearController.text.trim().isNotEmpty) {
      try {
        int year = int.parse(yearController.text.trim());
        int currentYear = DateTime.now().year;
        
        // Check if year is reasonable (between 1900 and current year - 13)
        if (year < 1900 || year > currentYear - 13) {
          showValidationError.value = true;
          isValid = false;
        }
      } catch (e) {
        // Invalid year format
        showValidationError.value = true;
        isValid = false;
      }
    }

    if (kDebugMode) {
      print("Form validation result: $isValid");
    }

    return isValid;
  }

  /// Save personal information data
  /// 
  /// Validates form data and saves it using UserDataController
  /// Navigates to home page on successful save
  Future<void> savePersonalInfo() async {
    if (!validateForm()) {
      if (kDebugMode) {
        print("Form validation failed");
      }
      return;
    }

    try {
      startLoading();

      // Prepare data to update
      Map<String, dynamic> userInfo = {
        'name': nameController.text.trim(),
        'nationality': nationalityController.text.trim(),
        'birthdayYear': yearController.text.trim(),
        'gender': gender,
      };

      // Update user data via UserDataController
      await _userDataController.updateUserData(userInfo);

      // Navigate to home page on success
      Get.offAll(const HomePage());

      if (kDebugMode) {
        print("Personal info saved successfully");
      }
    } catch (e) {
      if (kDebugMode) print('Error saving personal info: $e');
      // Let the UI handle error display
      rethrow;
    } finally {
      stopLoading();
    }
  }

  /// Clear all form data
  /// 
  /// Resets all form controllers and validation state
  /// Used when logging out or switching users
  void clearFormData() {
    nameController.clear();
    nationalityController.clear();
    yearController.clear();
    locationController.clear();
    emailController.clear();

    gender = null;
    showGenderValidation.value = false;
    showValidationError.value = false;

    if (kDebugMode) {
      print("Form data cleared");
    }
  }

  /// Set gender selection
  /// 
  /// Updates the gender field and clears gender validation error
  /// 
  /// @param selectedGender The selected gender value
  void setGender(String selectedGender) {
    gender = selectedGender;
    showGenderValidation.value = false; // Clear validation error
    
    if (kDebugMode) {
      print("Gender set to: $selectedGender");
    }
  }

  /// Get formatted location string
  /// 
  /// Returns a formatted location string from the user model
  /// 
  /// @return Formatted location string or empty string if no location
  String getFormattedLocation() {
    final userLocation = _userDataController.userModel.value.userLocation;
    
    if (userLocation == null) return "";
    
    return "${userLocation.district}, ${userLocation.city}, ${userLocation.country}";
  }

  /// Check if form has unsaved changes
  /// 
  /// Compares current form values with saved user data
  /// 
  /// @return True if there are unsaved changes, false otherwise
  bool hasUnsavedChanges() {
    final userModel = _userDataController.userModel.value;
    
    return nameController.text.trim() != (userModel.name ?? "") ||
           nationalityController.text.trim() != (userModel.nationality ?? "") ||
           yearController.text.trim() != (userModel.birthdayYear ?? "") ||
           gender != userModel.gender;
  }

  /// Auto-save form data
  /// 
  /// Automatically saves form data if there are changes and form is valid
  /// Used for auto-save functionality
  Future<void> autoSaveIfNeeded() async {
    if (hasUnsavedChanges() && validateForm()) {
      try {
        Map<String, dynamic> userInfo = {
          'name': nameController.text.trim(),
          'nationality': nationalityController.text.trim(),
          'birthdayYear': yearController.text.trim(),
          'gender': gender,
        };

        await _userDataController.updateUserData(userInfo);
        
        if (kDebugMode) {
          print("Form auto-saved successfully");
        }
      } catch (e) {
        if (kDebugMode) print('Error auto-saving form: $e');
      }
    }
  }
}
