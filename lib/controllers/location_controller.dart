import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/personal_info_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/location_service.dart';
import 'package:towasl/views/personal_info_page/personal_info_page.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';
import 'package:geocoding/geocoding.dart' as geo;
import 'package:permission_handler/permission_handler.dart'
    as permission_handler;

/// Location Controller
/// Manages user location data, permissions, and geocoding
/// Handles fetching, saving, and updating user location information
class LocationController extends GetxController {
  // Services and Controllers
  final UserRepository _userRepository;
  final LocationService _locationService;
  final AppController _appController;
  final UserController _userController;

  // State variables
  var isLoading = false.obs;
  RxBool isOpenAppSetting = false.obs;

  // Location data variables
  RxDouble lat = 0.0.obs;
  RxDouble lng = 0.0.obs;
  RxString country = ''.obs;
  RxString city = ''.obs;
  RxString district = ''.obs;

  /// Constructor with dependency injection
  ///
  /// @param userRepository The user repository for data operations
  /// @param locationService The location service for location operations
  /// @param appController The app controller for global state
  /// @param userController The user controller for user operations
  LocationController({
    required UserRepository userRepository,
    required LocationService locationService,
    required AppController appController,
    required UserController userController,
  })  : _userRepository = userRepository,
        _locationService = locationService,
        _appController = appController,
        _userController = userController {
    // Initialize location variables
    lat = 0.0.obs;
    lng = 0.0.obs;
    country = ''.obs;
    city = ''.obs;
    district = ''.obs;
  }

  /// Save user location data to Firestore
  ///
  /// Updates the user's location information and navigates to the appropriate screen
  /// based on context (editing profile or initial setup)
  ///
  /// @param lat Latitude coordinate
  /// @param lng Longitude coordinate
  /// @param country Country name
  /// @param city City name
  /// @param district District/neighborhood name
  /// @param isFromEdit Whether this is being called from profile editing (true) or initial setup (false)
  Future<void> setLocationData({
    required double lat,
    required double lng,
    required String country,
    required String city,
    required String district,
    bool? isFromEdit,
  }) async {
    try {
      isLoading(true);

      // Create location data map
      Map<String, dynamic> userLocation = {
        "user_location": {
          "lat": lat,
          "lng": lng,
          "country": country,
          "city": city,
          "district": district,
        }
      };

      // Validate location data
      if (userLocation['user_location'].toString() == "{}") {
        ToastCustom.warningToast('allow_location'.tr);
      } else {
        // Update user location using repository
        await _userRepository.updateUserLocation(
            _appController.userId, userLocation);

        // Navigate based on context
        if ((isFromEdit ?? false) == false) {
          // Initial setup - go to personal info page
          Get.off(() => const PersonalInfoPage(),
              binding: PersonalInfoBinding());
        } else {
          // Editing profile - refresh user data
          await _userController.getUserData();
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error setting location data: $e');
      }
      ToastCustom.errorToast('Error updating location: $e');
    } finally {
      isLoading(false);
    }
  }

  /// Get the user's current location
  ///
  /// Handles location permissions, service status, and geocoding
  /// Saves location data or updates controller variables based on context
  ///
  /// @param isFromEdit Whether this is being called from profile editing
  /// @param isFromLocationPage Whether this is being called from the location page
  Future<void> getCurrentLocation(
      {bool? isFromEdit, bool? isFromLocationPage}) async {
    isLoading(true);

    try {
      // Check if location services are enabled
      bool serviceEnabled = await _locationService.isLocationServiceEnabled();
      if (!serviceEnabled) {
        // Request to enable location services
        serviceEnabled = await _locationService.requestLocationPermission();
        if (!serviceEnabled) {
          // User declined to enable location services
          ToastCustom.errorToast('need_allow_location'.tr);
          if ((isFromEdit ?? false) == false) {
            // In initial setup flow, retry after delay
            Future.delayed(const Duration(seconds: 4)).then((value) {
              getCurrentLocation(
                  isFromEdit: isFromEdit,
                  isFromLocationPage: isFromLocationPage);
            });
          }
          isLoading(false);
          return;
        }
      }

      // Check location permission status
      bool permissionGranted =
          await _locationService.isLocationPermissionGranted();
      if (!permissionGranted) {
        // Request location permission
        permissionGranted = await _locationService.requestLocationPermission();

        // Handle permission denial
        if (!permissionGranted) {
          // Permission denied
          lat.value = 0.0;
          ToastCustom.errorToast('need_allow_location'.tr);
          isOpenAppSetting.value = true;

          // Open app settings if in initial setup or on location page while editing
          if ((isFromEdit ?? false) == false ||
              ((isFromLocationPage ?? false) && (isFromEdit ?? false))) {
            Future.delayed(const Duration(seconds: 4)).then((value) async {
              // Open system settings to allow user to enable permissions
              await permission_handler.openAppSettings();
            });
          }
          isLoading(false);
          return;
        }
      }

      // Handle location retrieval based on environment
      if (kDebugMode) {
        // Use mock location data in debug mode (simulator support)
        if ((isFromEdit ?? false) == false) {
          // In initial setup flow, save the mock location data
          await setLocationData(
              isFromEdit: false,
              lat: 24.680459665769533,
              lng: 46.579983324072145,
              country: "المملكة العربية السعودية",
              city: "الرياض",
              district: "عرقة");
        } else {
          // In edit flow, just update the controller variables
          lat.value = 24.680459665769533;
          lng.value = 46.579983324072145;
          country.value = "المملكة العربية السعودية";
          city.value = "الرياض";
          district.value = "عرقة";
        }
      } else {
        // Production environment - get actual device location
        final locationData = await _locationService.getCurrentLocation();

        // Set locale for Arabic place names
        geo.setLocaleIdentifier("ar_SA");

        if ((isFromEdit ?? false) == false) {
          // Initial setup flow - save location data
          if (kDebugMode) {
            print("isFromEdit:$isFromEdit");
          }
          await setLocationData(
              isFromEdit: isFromEdit ?? false,
              lat: locationData.lat,
              lng: locationData.lng,
              country: locationData.country,
              city: locationData.city,
              district: locationData.district);
        } else {
          // Edit flow - update controller variables
          lat.value = locationData.lat;
          lng.value = locationData.lng;
          country.value = locationData.country;
          city.value = locationData.city;
          district.value = locationData.district;

          // If on location page while editing, return to previous screen
          if ((isFromLocationPage ?? false) && (isFromEdit ?? false)) {
            Get.back(result: true);
          }
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print("Caught error in location operations: $e");
      }
      ToastCustom.errorToast('Error getting location: $e');
    } finally {
      isLoading(false);
    }
  }
}
