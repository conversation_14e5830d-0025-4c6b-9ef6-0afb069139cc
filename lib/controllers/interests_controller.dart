import 'dart:convert';
import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/interests_repository.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/models/interest_model.dart';
import 'package:towasl/views/location_page.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// Interests Controller
/// Manages user interests data and operations
/// Handles fetching, saving, and updating user interest selections
class InterestsController extends GetxController {
  // Services and Controllers
  final InterestsRepository _interestsRepository;
  final UserRepository _userRepository;
  final AppController _appController;
  final UserController _userController;

  /// Constructor with dependency injection
  ///
  /// @param interestsRepository The interests repository for data operations
  /// @param userRepository The user repository for user data operations
  /// @param appController The app controller for global state
  /// @param userController The user controller for user operations
  InterestsController({
    required InterestsRepository interestsRepository,
    required UserRepository userRepository,
    required AppController appController,
    required UserController userController,
  })  : _interestsRepository = interestsRepository,
        _userRepository = userRepository,
        _appController = appController,
        _userController = userController {
    if (kDebugMode) {
      print("=== InterestsController created ===");
      print("interestsRepository: $_interestsRepository");
      print("userRepository: $_userRepository");
      print("appController: $_appController");
      print("userController: $_userController");
    }
  }

  // Observable list of interest categories and subcategories
  RxList<InterestModel> interestData = <InterestModel>[].obs;

  // Loading state
  var isLoading = true.obs;

  /// Fetch interest categories and subcategories from Firestore
  ///
  /// Retrieves all available interest categories and marks those already
  /// selected by the user if in settings mode
  ///
  /// @param isFromSetting Whether this is being called from settings (true) or initial setup (false)
  Future<void> getInterestData(bool isFromSetting) async {
    if (kDebugMode) {
      print("=== InterestsController.getInterestData called ===");
      print("isFromSetting: $isFromSetting");
      print("_interestsRepository: $_interestsRepository");
    }

    // Start loading and clear previous data
    isLoading(true);
    interestData.clear();

    // Removed unnecessary delay

    try {
      if (kDebugMode) {
        print("About to call _interestsRepository.getVisibleInterests()");
      }

      // Fetch all visible interest categories using repository
      final interests = await _interestsRepository.getVisibleInterests();

      if (kDebugMode) {
        print("Fetched ${interests.length} interests from repository");
        print(jsonEncode(interests));
      }

      // Assign the fetched interests to the observable list
      interestData.assignAll(interests);

      if (kDebugMode) {
        print("interestData.length after assignment: ${interestData.length}");
      }

      // If in settings mode, mark user's previously selected interests
      if (isFromSetting) {
        for (var interest in interestData) {
          final userInterests = _userController.userModel.value.userInterest;

          // Check if user has selections in this category
          if (userInterests != null &&
              userInterests.containsKey(interest.id.toString())) {
            // Mark subcategories as selected if they match user's selections
            for (var subCategory in interest.subCategories ?? []) {
              if (userInterests[interest.id.toString()]
                      ?.contains(subCategory.subcategory) ??
                  false) {
                subCategory.isSelected = true;
              }
            }
          }
        }
      }

      // Update UI with the loaded data
      interestData.refresh();

      if (kDebugMode) {
        print("interestData: \${interestData.length}");
      }
    } catch (e) {
      ToastCustom.errorToast(e.toString());
    } finally {
      isLoading(false);
    }
  }

  /// Save user's selected interests to Firestore
  ///
  /// Processes the user's interest selections and updates their profile
  /// Navigates to the appropriate screen based on context
  ///
  /// @param interestData List of interest categories with selected subcategories
  /// @param isFromSetting Whether this is being called from settings (true) or initial setup (false)
  Future<void> setInterestData(List<InterestModel> interestData,
      {bool? isFromSetting}) async {
    try {
      // Set loading state to true to show the overlay
      isLoading(true);

      // Prepare data structure for Firestore update
      Map<String, dynamic> userInterest = {};
      userInterest['user_interest'] = {};

      // Process each interest category
      for (var element in interestData) {
        // Process each subcategory
        for (var element2 in element.subCategories!) {
          // Only include selected subcategories
          if (element2.isSelected ?? false) {
            List<String> subCatList = [];

            // Check if category already has selections
            if (userInterest['user_interest']
                .containsKey(element.id.toString())) {
              // Add to existing category selections
              subCatList = userInterest['user_interest'][element.id.toString()];
              subCatList.add(element2.subcategory ?? '');
            } else {
              // Create new category selection
              subCatList.add(element2.subcategory ?? '');
            }

            // Update the data structure
            userInterest['user_interest'][element.id.toString()] = subCatList;
          }
        }
      }

      // Validate that at least one interest is selected
      if (userInterest['user_interest'].toString() == "{}") {
        ToastCustom.warningToast('Please select your interests first'.tr);
        // Set loading state to false if validation fails
        isLoading(false);
      } else {
        // Removed intentional delay that was making the loading overlay show longer than needed

        // Update user interests using repository
        await _userRepository.updateUserInterests(
            _appController.userId, userInterest);

        if (kDebugMode) {
          print('interest updated');
        }

        // Removed unnecessary delay before navigation

        // Navigate based on context
        if (isFromSetting ?? false) {
          // In settings mode, refresh user data and go back
          await _userController.getUserData();
          Get.back();
        } else {
          // In initial setup, proceed to location page
          Get.off(const LocationPage(
            isFromEdit: false,
          ));
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('error: $e');
      }
      // Show error toast
      ToastCustom.errorToast('Error saving interests: ${e.toString()}');
    } finally {
      // Set loading state to false only if it hasn't been set already
      if (isLoading.value) {
        isLoading(false);
      }
    }
  }
}
