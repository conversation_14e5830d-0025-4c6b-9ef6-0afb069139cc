/// Mobile Login Controller
///
/// Handles authentication using mobile number
/// Provides methods for login and registration with mobile
library mobile_login_controller;

import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/mobile_otp_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/auth_controllers/save_register_data_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/views/mobile_otp_page/mobile_otp_page.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// Controller for handling mobile authentication
class MobileLoginController extends BaseController {
  /// User repository for data operations
  final UserRepository _userRepository;

  /// App controller for global state
  final AppController _appController;

  /// Constructor
  MobileLoginController({
    required UserRepository userRepository,
    required AppController appController,
  })  : _userRepository = userRepository,
        _appController = appController;

  /// Sign in with mobile number
  ///
  /// Handles the mobile authentication flow
  /// Checks if user exists and creates a new user if needed
  /// Only navigates to OTP verification page for new users (signup)
  /// For existing users (login), proceeds directly to save registration data
  ///
  /// @param mobileNumber The user's mobile number
  /// @param context The BuildContext for showing toasts
  Future<void> signInWithMobile({
    required String mobileNumber,
    required BuildContext context,
  }) async {
    startLoading();

    try {
      // Check if user with this mobile number already exists
      final userExists = await _userExistsWithMobile(mobileNumber);
      String userId;

      if (userExists) {
        // User exists, proceed with login directly (no OTP for login)
        if (kDebugMode) {
          print("User exists with mobile: $mobileNumber");
          print("User ID: ${_appController.userId}");
        }

        // Use existing user ID
        userId = _appController.userId;

        // Get the SaveRegisterDataController to handle login flow
        SaveRegisterDataController saveRegisterDataController = Get.find();

        // Set the user ID in the app controller
        _appController.userId = userId;

        // Proceed with login directly
        await saveRegisterDataController.saveRegisterData(
          mobile: mobileNumber,
          userId: userId,
        );
      } else {
        // User doesn't exist, create new user and verify with OTP
        // Generate a random user ID
        userId = _generateRandom6CharId();

        if (kDebugMode) {
          print("New user with mobile: $mobileNumber");
          print("Generated User ID: $userId");
        }

        // Navigate to OTP verification page for new users only
        Get.to(
          () => const MobileOtpPage(),
          binding: MobileOtpBinding(
            mobileNumber: mobileNumber,
            userId: userId,
          ),
          transition: Transition.rightToLeft,
        );
      }
    } catch (e) {
      // Log and display error
      if (kDebugMode) {
        print("Error in signInWithMobile: $e");
      }
      ToastCustom.errorToast(e.toString());
    } finally {
      stopLoading();
    }
  }

  /// Check if a user with the given mobile number already exists
  ///
  /// Uses repository to find a user with the specified mobile number
  /// If found, sets the AppController userId to the user's ID
  ///
  /// @param mobileNumber The mobile number to check
  /// @return True if user exists, false otherwise
  Future<bool> _userExistsWithMobile(String mobileNumber) async {
    try {
      if (kDebugMode) {
        print("Checking mobile: $mobileNumber");
      }

      // Check if user exists via repository
      final userId = await _userRepository.getUserIdByMobile(mobileNumber);

      if (userId != null) {
        // User exists, set user ID in app controller
        _appController.userId = userId;
        return true;
      } else {
        // User doesn't exist
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error checking if user exists: $e");
      }
      return false;
    }
  }

  /// Generate a random 6-character ID
  ///
  /// Used to create unique user IDs
  ///
  /// @return A random 6-character string
  String _generateRandom6CharId() {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    final random = Random();
    return String.fromCharCodes(
      Iterable.generate(
        6,
        (_) => chars.codeUnitAt(random.nextInt(chars.length)),
      ),
    );
  }
}
