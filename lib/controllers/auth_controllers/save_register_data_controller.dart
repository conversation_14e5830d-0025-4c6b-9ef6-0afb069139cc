/// Registration Data Controller
///
/// Handles saving user registration data to Firebase
/// and navigating to the appropriate screen based on profile completion
library save_register_data_controller;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/repositories/auth_repository.dart';
import 'package:towasl/core/repositories/user_repository.dart';
import 'package:towasl/core/services/auth_navigation_service.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/storage_service.dart';

import 'package:towasl/views/widgets/toasts_custom.dart';

/// Controller for saving user registration data
///
/// Extends BaseController to utilize loading state management
/// Handles user registration, checking existing users, and session management
class SaveRegisterDataController extends BaseController {
  // Services
  final FirebaseService _firebaseService;
  final AuthRepository _authRepository;
  final UserRepository _userRepository;
  final AppController _appController;
  final StorageService _storageService;
  final AuthNavigationService _authNavigationService;

  /// Constructor with dependency injection
  ///
  /// @param firebaseService The Firebase service for database operations
  /// @param authRepository The auth repository for authentication operations
  /// @param userRepository The user repository for data operations
  /// @param appController The app controller for global state
  /// @param storageService The storage service for persistent data
  /// @param authNavigationService The auth navigation service for navigation
  SaveRegisterDataController({
    required FirebaseService firebaseService,
    required AuthRepository authRepository,
    required UserRepository userRepository,
    required AppController appController,
    required StorageService storageService,
    required AuthNavigationService authNavigationService,
  })  : _firebaseService = firebaseService,
        _authRepository = authRepository,
        _userRepository = userRepository,
        _appController = appController,
        _storageService = storageService,
        _authNavigationService = authNavigationService;

  /// Save user registration data to Firebase
  ///
  /// Checks if user already exists, and either:
  /// 1. Creates a new user document if the user doesn't exist
  /// 2. Loads existing user data if the user already exists
  /// Then navigates to the appropriate screen based on profile completion
  ///
  /// @param mobile User's mobile number
  /// @param userId Unique user ID to be used as document ID
  Future<void> saveRegisterData(
      {required String mobile, required String userId}) async {
    startLoading();
    try {
      // Prepare basic user data
      Map<String, dynamic> userData = {
        "mobile": mobile,
        "user_id": userId,
        "is_blocked": false,
        "create_at": DateTime.now().toString(),
      };

      // Check if user already exists with this mobile number
      final existingUserId = await _authRepository.getUserIdByMobile(mobile);
      if (existingUserId != null) {
        // User exists, set the user ID in app controller
        _appController.userId = existingUserId;

        if (kDebugMode) {
          print(_appController.userId);
        }

        // User exists, save login data to storage
        // Commented code for reference:
        // userID=userId;
        // addBlockedField(_appController.userId);
        _storageService.setLoginData(_appController.userId);

        // Initialize controllers with proper bindings
        Get.put<UserController>(UserController(
          userRepository: _userRepository,
          appController: _appController,
          storageService: _storageService,
        ));

        // Load user data
        UserController userController = Get.find<UserController>();
        await userController.getUserData();

        // Set Firebase token for session management
        setFirebaseTokenForUserSession();

        // Navigate based on profile completion status
        await _authNavigationService
            .navigateBasedOnProfile(userController.userModel.value);
      } else {
        // User doesn't exist, create new user document
        _userRepository.createUser(userId, userData).then((value) async {
          // Set user ID in AppController
          _appController.userId = userId;

          // Set Firebase token for session management
          setFirebaseTokenForUserSession();

          // Save login data to storage
          _storageService.setLoginData(_appController.userId);

          // Navigate to interests page to start profile setup
          await _authNavigationService.navigateToInterests();
        }).catchError((error) {
          ToastCustom.errorToast(error.toString());
        });
      }
    } catch (e) {
      ToastCustom.errorToast(e.toString());
    }
    stopLoading();
  }

  /// Add or update the is_blocked field for a user
  ///
  /// Sets the is_blocked field to false for the specified user
  /// Used to ensure all users have this field for filtering
  ///
  /// @param userId The ID of the user to update
  Future<void> addBlockedField(String userId) async {
    try {
      await _authRepository.updateUserBlockStatus(userId, false);

      if (kDebugMode) {
        print("blocked field added");
      }
    } catch (error) {
      ToastCustom.errorToast(error.toString());
    }
  }

  /// Set Firebase token for user session management
  ///
  /// Updates the user's session_token field with their FCM token + userID
  /// This helps track active sessions and enables session validation
  Future<void> setFirebaseTokenForUserSession() async {
    if (kDebugMode) print('setFirebaseTokenForUserSession: Entered');

    // Get FCM token and combine with user ID to create a unique session token
    final token = await _firebaseService.getToken() ?? "";
    final sessionToken = token + _appController.userId;

    // Update the user document with the session token via repository
    await _userRepository.updateSessionToken(
        _appController.userId, sessionToken);

    if (kDebugMode) {
      print('session updated:${_appController.userId}');
    }
  }
}
