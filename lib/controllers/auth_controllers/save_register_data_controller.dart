/// Registration Data Controller
///
/// Handles saving user registration data to Firebase
/// and navigating to the appropriate screen based on profile completion
library save_register_data_controller;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/home_binding.dart';
import 'package:towasl/bindings/interests_binding.dart';
import 'package:towasl/bindings/location_binding.dart';
import 'package:towasl/bindings/personal_info_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/views/home_page/home_page.dart';
import 'package:towasl/views/personal_info_page/personal_info_page.dart';
import 'package:towasl/views/location_page.dart';
import 'package:towasl/views/interests_page.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// Controller for saving user registration data
///
/// Extends BaseController to utilize loading state management
/// Handles user registration, checking existing users, and session management
class SaveRegisterDataController extends BaseController {
  // Services
  final FirebaseService _firebaseService;
  final AppController _appController;
  final StorageService _storageService;

  /// Constructor with dependency injection
  ///
  /// @param firebaseService The Firebase service for database operations
  /// @param appController The app controller for global state
  /// @param storageService The storage service for persistent data
  SaveRegisterDataController({
    required FirebaseService firebaseService,
    required AppController appController,
    required StorageService storageService,
  })  : _firebaseService = firebaseService,
        _appController = appController,
        _storageService = storageService;

  /// Save user registration data to Firebase
  ///
  /// Checks if user already exists, and either:
  /// 1. Creates a new user document if the user doesn't exist
  /// 2. Loads existing user data if the user already exists
  /// Then navigates to the appropriate screen based on profile completion
  ///
  /// @param mobile User's mobile number
  /// @param userId Unique user ID to be used as document ID
  Future<void> saveRegisterData(
      {required String mobile, required String userId}) async {
    startLoading();
    try {
      // Prepare basic user data
      Map<String, dynamic> userData = {
        "mobile": mobile,
        "user_id": userId,
        "is_blocked": false,
        "create_at": DateTime.now().toString(),
      };

      // Check if user already exists with this mobile number
      if (await _userExistsPhone(mobile)) {
        if (kDebugMode) {
          print(_appController.userId);
        }

        // User exists, save login data to storage
        // Commented code for reference:
        // userID=userId;
        // addBlockedField(_appController.userId);
        _storageService.setLoginData(_appController.userId);

        // Initialize controllers with proper bindings
        Get.put<UserController>(UserController(
          firebaseService: _firebaseService,
          appController: _appController,
          storageService: _storageService,
        ));

        // Load user data
        UserController userController = Get.find<UserController>();
        await userController.getUserData();

        // Set Firebase token for session management
        setFirebaseTokenForUserSession();

        // Navigate based on profile completion status
        if (userController.userModel.value.userInterest == null) {
          // User needs to select interests
          Get.offAll(() => const InterestsPage(), binding: InterestsBinding());
        } else if (userController.userModel.value.userLocation == null) {
          // User needs to set location
          Get.offAll(() => const LocationPage(
            isFromEdit: false,
          ), binding: LocationBinding());
        } else if ((userController.userModel.value.nationality ?? "") == '') {
          // User needs to complete personal information
          Get.offAll(() => const PersonalInfoPage(), binding: PersonalInfoBinding());
        } else {
          // User profile is complete, go to home page
          Get.offAll(() => const HomePage(), binding: HomeBinding());
        }
      } else {
        // User doesn't exist, create new user document
        _firebaseService.setDocument("users", userId, userData).then((value) {
          // Set user ID in AppController
          _appController.userId = userId;

          // Set Firebase token for session management
          setFirebaseTokenForUserSession();

          // Save login data to storage
          _storageService.setLoginData(_appController.userId);

          // Navigate to interests page to start profile setup
          Get.offAll(() => const InterestsPage(), binding: InterestsBinding());
        }).catchError((error) {
          ToastCustom.errorToast(error.toString());
        });
      }
    } catch (e) {
      ToastCustom.errorToast(e.toString());
    }
    stopLoading();
  }

  /// Add or update the is_blocked field for a user
  ///
  /// Sets the is_blocked field to false for the specified user
  /// Used to ensure all users have this field for filtering
  ///
  /// @param userId The ID of the user to update
  Future<void> addBlockedField(String userId) async {
    try {
      await _firebaseService.updateDocument(
        "users",
        userId,
        {"is_blocked": false}
      );

      if (kDebugMode) {
        print("blocked field added");
      }
    } catch (error) {
      ToastCustom.errorToast(error.toString());
    }
  }

  /// Check if a user with the given mobile number already exists
  ///
  /// Queries Firestore to find a user with the specified mobile number
  /// If found, sets the AppController userId to the user's ID
  ///
  /// @param mobile The mobile number to check
  /// @return True if user exists, false otherwise
  Future<bool> _userExistsPhone(String mobile) async {
    try {
      // First try to find user with mobile field
      var snapshot = await _firebaseService.firestore
          .collection("users")
          .where("mobile", isEqualTo: mobile)
          .limit(1)
          .get();

      // If no results, try legacy email field (for backward compatibility)
      if (snapshot.docs.isEmpty) {
        snapshot = await _firebaseService.firestore
            .collection("users")
            .where("email", isEqualTo: mobile)
            .limit(1)
            .get();
      }

      if (kDebugMode) {
        print("mobile:$mobile");
      }

      if (snapshot.docs.isNotEmpty) {
        // User exists, extract user ID
        var userData = snapshot.docs.first.data();
        _appController.userId = userData['user_id'];
        return true;
      } else {
        // User doesn't exist
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking user existence: $e');
      }
      return false;
    }
  }
  /// Set Firebase token for user session management
  ///
  /// Updates the user's session_token field with their FCM token + userID
  /// This helps track active sessions and enables session validation
  Future<void> setFirebaseTokenForUserSession() async {
    if (kDebugMode) print('setFirebaseTokenForUserSession: Entered');

    // Get FCM token and combine with user ID to create a unique session token
    final token = await _firebaseService.getToken() ?? "";
    final sessionToken = token + _appController.userId;

    // Update the user document with the session token
    await _firebaseService.updateDocument(
      "users",
      _appController.userId,
      {'session_token': sessionToken}
    );

    if (kDebugMode) {
      print('session updated:${_appController.userId}');
    }
  }
}
