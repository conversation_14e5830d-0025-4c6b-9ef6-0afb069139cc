/// OTP Verification Controller
///
/// Handles verification of one-time passwords sent to mobile numbers
/// Manages OTP verification, resending, and timer functionality
library otp_verification_controller;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/auth_controllers/save_register_data_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// Controller for OTP verification
class OtpVerificationController extends BaseController {
  /// Mobile number to which <PERSON><PERSON> was sent
  final String mobileNumber;

  /// User ID for the account being verified
  final String userId;

  /// Timer for OTP expiration countdown
  Timer? _timer;

  /// Remaining seconds for OTP validity
  final RxInt remainingSeconds = 80.obs;

  /// Whether OTP verification is in progress
  final RxBool isVerifying = false.obs;

  /// Whether OTP has been verified successfully
  final RxBool isVerified = false.obs;

  /// App controller for global state
  final AppController _appController;

  /// Constructor
  OtpVerificationController({
    required this.mobileNumber,
    required this.userId,
    required AppController appController,
  }) : _appController = appController {
    // Start the countdown timer when controller is created
    startTimer();
  }

  /// Start the countdown timer for OTP expiration
  void startTimer() {
    // Cancel any existing timer
    _timer?.cancel();

    // Reset the countdown to 80 seconds (1:20)
    remainingSeconds.value = 80;

    // Create a new timer that ticks every second
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (remainingSeconds.value > 0) {
        // Decrement the remaining time
        remainingSeconds.value--;
      } else {
        // Stop the timer when countdown reaches zero
        timer.cancel();
      }
    });
  }

  /// Format the remaining seconds as MM:SS
  String get formattedTime {
    int minutes = remainingSeconds.value ~/ 60;
    int seconds = remainingSeconds.value % 60;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
  }

  /// Verify the entered OTP
  ///
  /// In a real implementation, this would validate against a server
  /// For now, we'll simulate verification with any 4-digit code
  ///
  /// @param otp The OTP entered by the user
  Future<void> verifyOtp(String otp) async {
    // Set verification in progress
    isVerifying.value = true;

    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 800));

      // For demo purposes, any 4-digit code is accepted
      // In a real app, this would validate against a server
      if (otp.length == 4) {
        // OTP is valid
        isVerified.value = true;

        // Get the SaveRegisterDataController to handle login flow
        SaveRegisterDataController saveRegisterDataController = Get.find();

        // Set the user ID in the app controller
        _appController.userId = userId;

        // Proceed with registration/login
        await saveRegisterDataController.saveRegisterData(
          mobile: mobileNumber,
          userId: userId,
        );
      } else {
        // OTP is invalid
        ToastCustom.errorToast('incorrect_otp_error'.tr);
      }
    } catch (e) {
      // Log and display error
      if (kDebugMode) {
        print("Error in verifyOtp: $e");
      }
      ToastCustom.errorToast(e.toString());
    } finally {
      // Reset verification in progress
      isVerifying.value = false;
    }
  }

  /// Resend OTP to the mobile number
  ///
  /// Simulates resending an OTP and resets the timer
  Future<void> resendOtp() async {
    // Check if timer is still running
    if (remainingSeconds.value > 0) {
      ToastCustom.warningToast('avoid_blockage'.tr);
      return;
    }

    try {
      // Set loading state
      startLoading();

      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 800));

      // Reset and restart the timer
      startTimer();

      // Show success message
      ToastCustom.successToast('OTP resent successfully'.tr);
    } catch (e) {
      // Log and display error
      if (kDebugMode) {
        print("Error in resendOtp: $e");
      }
      ToastCustom.errorToast(e.toString());
    } finally {
      // Reset loading state
      stopLoading();
    }
  }

  @override
  void onClose() {
    // Cancel the timer when controller is disposed
    _timer?.cancel();
    super.onClose();
  }
}
