import 'package:get/get.dart';

/// Base Controller
/// Provides common functionality for all controllers
/// Handles loading state management
class BaseController extends GetxController {
  // Observable loading state
  RxBool isLoading = false.obs;

  /// Sets the loading state to true
  ///
  /// Used to indicate that an operation is in progress
  void startLoading() {
    isLoading.value = true;
  }

  /// Sets the loading state to false
  ///
  /// Used to indicate that an operation has completed
  void stopLoading() {
    isLoading.value = false;
  }
}
