/// Firebase Error Handler
///
/// This file contains error handling logic for Firebase operations
/// including permission errors and other Firestore exceptions
library firebase_error_handler;

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

/// Firestore Error Handler
///
/// Manages Firestore settings and error handling
/// Detects permission denied errors and other Firestore exceptions
class FireStoreErrorHandler extends GetxController {
  /// Flag to track if permission has been denied
  var isPermissionDenied = false.obs;

  @override
  void onInit() {
    super.onInit();

    // Configure Firestore settings for offline persistence
    FirebaseFirestore.instance.settings = const Settings(
      persistenceEnabled: true,
      cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED, // Use unlimited cache size
    );

    // Set up a listener for Firestore sync events to catch errors
    FirebaseFirestore.instance.snapshotsInSync().listen(
      // Success callback (empty as we're only interested in errors)
      (_) {},
      // Error callback
      onError: (error) {
        // Log all errors in debug mode
        if (kDebugMode) {
          print("error: new1: $error");
        }

        // Specifically handle permission denied errors
        if (error is FirebaseException &&
            error.message != null &&
            error.message!.contains('permission-denied')) {
          if (kDebugMode) {
            print("error: new: $error");
          }
          // Here you could set isPermissionDenied to true and handle the UI accordingly
        }
      }
    );
  }
}
