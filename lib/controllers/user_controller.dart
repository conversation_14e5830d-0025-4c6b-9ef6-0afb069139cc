import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:towasl/bindings/signup_login_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/base_controller.dart';
import 'package:towasl/core/services/firebase_service.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/models/user_model.dart';
import 'package:towasl/views/home_page/home_page.dart';
import 'package:towasl/views/signup_login_page/signup_login_page.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// User Controller
/// Manages user data, authentication, and profile operations
/// Handles fetching, updating, and deleting user information
class UserController extends BaseController {
  // Services
  final FirebaseService _firebaseService;
  final AppController _appController;
  final StorageService _storageService;

  /// Constructor with dependency injection
  ///
  /// @param firebaseService The Firebase service for database operations
  /// @param appController The app controller for global state
  /// @param storageService The storage service for persistent data
  UserController({
    required FirebaseService firebaseService,
    required AppController appController,
    required StorageService storageService,
  })  : _firebaseService = firebaseService,
        _appController = appController,
        _storageService = storageService;

  // State variables
  Rx<UserModel> userModel = UserModel().obs;
  List<String> userInterestList = [];
  RxBool isNotiEnabled = false.obs;

  // Form controllers for user profile data
  TextEditingController nameController = TextEditingController();
  TextEditingController nationalityController = TextEditingController();
  TextEditingController yearController = TextEditingController();
  TextEditingController locationController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  // User gender selection
  String? gender;
  RxBool showGenderValidation = false.obs;
  RxBool showValidationError = false.obs;

  @override
  void onInit() {
    super.onInit();
    gender = null;
    // Ensure validation flags are properly initialized
    showGenderValidation.value = false;
    showValidationError.value = false;
  }

  /// Initialize user data in form controllers
  /// Populates text controllers with data from the user model
  void initializeUserData() {
    // Set gender selection
    gender = userModel.value.gender;

    // Set text field values from user model
    nameController.text = userModel.value.name ?? "";
    nationalityController.text = userModel.value.nationality ?? "";
    yearController.text = userModel.value.birthdayYear ?? "";

    // Format location as district, city, country
    locationController.text = "${userModel.value.userLocation?.district}, "
        "${userModel.value.userLocation?.city}, "
        "${userModel.value.userLocation?.country}";

    emailController.text = userModel.value.mobile ?? "";
  }

  /// Fetch user data from Firestore
  /// Retrieves the current user's data and updates the user model
  Future<void> getUserData() async {
    // Check if user is logged in
    if (_appController.userId.isEmpty) {
      if (kDebugMode) print("No user logged in");
      return;
    }

    // Clear previous data and set loading state
    userInterestList.clear();
    startLoading();

    try {
      // Fetch user document from Firestore
      DocumentSnapshot snapshot =
          await _firebaseService.getDocument("users", _appController.userId);

      if (snapshot.exists) {
        // Parse document data into UserModel
        userModel.value = userModelFromJson(jsonEncode(snapshot.data()));

        // Extract all interests into a flat list for easier access
        userInterestList = List<String>.from(
            userModel.value.userInterest?.values.expand((i) => i) ?? []);

        // Update UI and global references
        userModel.refresh();
        _appController.userModel = userModel.value;

        // Initialize form controllers with user data
        initializeUserData();
      }
    } catch (e) {
      if (kDebugMode) print("Error fetching user data: $e");
    } finally {
      // End loading state
      stopLoading();
    }
  }

  /// Update user profile information
  /// Saves the user's personal information to Firestore and navigates to home page
  Future<void> setPersonalInfoData() async {
    try {
      startLoading();

      // Prepare data to update
      Map<String, dynamic> userInfo = {
        'name': nameController.text,
        'nationality': nationalityController.text,
        'birthdayYear': yearController.text,
        'gender': gender,
      };

      // Update user document in Firestore
      await _firebaseService.updateDocument(
          "users", _appController.userId, userInfo);

      // Refresh user data from server
      await getUserData();

      // Navigate to home page
      Get.offAll(const HomePage());
    } catch (e) {
      if (kDebugMode) print('Error updating profile: $e');
    } finally {
      stopLoading();
    }
  }

  /// Delete user account
  /// Removes user data from Firestore, signs out, and clears local data
  Future<void> deleteAccount() async {
    try {
      startLoading();

      // Delete user document from Firestore
      await _firebaseService.deleteDocument("users", _appController.userId);

      // Sign out (no-op implementation)
      await _firebaseService.signOut();

      // Clear app controller state
      _appController.clearUserData();

      // Clear local storage data
      await _storageService.clearUserData();

      // Reset controller data
      userModel.value = UserModel();
      userInterestList.clear();

      // Clear form controllers
      nameController.clear();
      nationalityController.clear();
      yearController.clear();
      locationController.clear();
      emailController.clear();

      // Reset gender and validation state
      gender = null;
      showGenderValidation.value = false;
      showValidationError.value = false;

      if (kDebugMode) {
        print(
            "Account deleted, gender validation state reset: ${showGenderValidation.value}");
      }

      // Navigate to login page
      Get.offAll(() => const SignupLoginPage(), binding: SignupLoginBinding());
    } catch (e) {
      if (kDebugMode) print("Error deleting account: $e");
    } finally {
      stopLoading();
    }
  }

  /// Set Firebase Cloud Messaging (FCM) token
  /// Updates the user's FCM token for push notifications and checks blocked status
  Future<void> setFcmToken(BuildContext context) async {
    // Update FCM token in user document
    await _firebaseService.updateDocument("users", _appController.userId,
        {'fcm_token': OneSignal.User.pushSubscription.id.toString()});

    // Check if user is blocked after updating token
    // ignore: use_build_context_synchronously
    checkUserBlockedStatus(context);
  }

  /// Check if user is blocked
  /// Sets up a real-time listener for user blocked status
  /// If blocked, logs out the user and shows a notification
  void checkUserBlockedStatus(BuildContext context) {
    // Create a query to check if the current user is blocked
    _firebaseService.firestore
        .collection("users")
        .where("is_blocked", isEqualTo: true)
        .where("user_id", isEqualTo: _appController.userId)
        .snapshots()
        .listen((event) async {
      // If user is found in blocked users
      if (event.docs.isNotEmpty) {
        // Clear app controller state
        _appController.clearUserData();

        // Clear local storage data
        await _storageService.clearUserData();

        // Navigate to login page
        Get.offAll(() => const SignupLoginPage(),
            binding: SignupLoginBinding());

        // Show blocked notification
        // ignore: use_build_context_synchronously
        ToastCustom.warningToastCustom("Number blocked".tr, context, true);
      }
    });
  }
}
