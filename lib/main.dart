import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:onesignal_flutter/onesignal_flutter.dart';
import 'package:towasl/bindings/app_binding.dart';
import 'package:towasl/core/constants.dart';
import 'package:towasl/core/config/onesignal_notification.dart';
import 'package:towasl/core/di/dependency_injection.dart';
import 'package:towasl/localization/translation.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/config/firebase_options.dart';
import 'package:towasl/views/splash_page.dart';

/// Application entry point
///
/// Initializes Firebase, OneSignal notifications, and launches the app
Future<void> main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase with platform-specific options
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Initialize GetStorage for persistent storage
  await GetStorage.init();

  // Initialize dependency injection
  await initDependencies();

  // Configure OneSignal push notifications
  OneSignal.Debug.setLogLevel(OSLogLevel.verbose);
  OneSignal.initialize(Constants.oneSignalAppID);
  OneSignal.Notifications.requestPermission(true);
  OneSignal.consentGiven(true);

  // Launch the application
  runApp(const MyApp());
}

/// Main application widget
///
/// Root widget that configures the application theme, localization,
/// and notification handlers
class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Set up notification click handlers
    OneSignalNotificationsClass.onForegroundNotificationClick();
    OneSignalNotificationsClass.onNotificationClick();
  }

  @override
  Widget build(BuildContext context) {
    // SYSTEM CONFIGURATION ----------
    // Set device orientation to portrait only
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Set status bar and system bar color
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: AppColors.primaryPurple,
      statusBarIconBrightness: Brightness.light,
    ));
    // SYSTEM CONFIGURATION ##########

    // APPLICATION CONFIGURATION ----------
    return GetMaterialApp(
      // Disable debug banner
      debugShowCheckedModeBanner: false,

      // Localization setup
      translations: AppTranslation(),
      locale: const Locale('ar', 'SA'),      // Default to Arabic
      fallbackLocale: const Locale('ar', 'SA'),

      // Navigation transition
      defaultTransition: Transition.cupertino,

      // App title
      title: 'Towasl',

      // Initialize global controllers
      initialBinding: AppBinding(),

      // Theme configuration
      theme: ThemeData(
        useMaterial3: true,

        // Scrollbar appearance
        scrollbarTheme: ScrollbarThemeData(
          thumbColor: WidgetStateProperty.all(AppColors.greyMedium),
        ),

        // AppBar theme
        appBarTheme: const AppBarTheme(
          backgroundColor: AppColors.primaryPurple,
          iconTheme: IconThemeData(color: AppColors.whitePure),
        ),
      ),

      // Initial screen
      home: const SplashPage(),
    );
    // APPLICATION CONFIGURATION ##########
  }
}
