/// Home Page
///
/// Main screen of the application after login
/// Displays user information and matches based on location
library home_page;

import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/home_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/views/home_page/widgets/location_not_support.dart';
import 'package:towasl/views/settings_page.dart';
import 'package:towasl/views/widgets/progress_indicator.dart';

/// Home Screen
///
/// Main screen of the application after login
/// Shows user ID and displays matches or location support message
class HomePage extends StatefulWidget {
  /// Creates a HomePage
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

/// State for the HomePage
class _HomePageState extends State<HomePage> {
  /// Controller for user-related operations
  late UserController userController;

  /// App controller for global state
  late AppController appController;

  /// Timer to handle loading timeout
  Timer? _loadingTimer;

  /// Flag to track if the component is mounted
  bool _isMounted = false;

  /// Flag to track if data is being loaded
  final RxBool _isLoading = true.obs;

  @override
  void initState() {
    super.initState();
    _isMounted = true;

    // Initialize controllers
    appController = Get.find<AppController>();
    userController = Get.find<UserController>();

    // Load user data after the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadUserData();
    });
  }

  @override
  void dispose() {
    _isMounted = false;
    _loadingTimer?.cancel();
    super.dispose();
  }

  /// Load user data with timeout protection
  void _loadUserData() async {
    if (!_isMounted) return;

    // Set local loading state
    _isLoading.value = true;

    // Reset any existing loading state in the controller
    if (userController.isLoading.value) {
      userController.stopLoading();
    }

    // Set a timeout to prevent infinite loading
    _loadingTimer = Timer(const Duration(seconds: 8), () {
      if (_isMounted) {
        if (kDebugMode) {
          print("Loading timeout occurred in HomePage");
        }
        userController.stopLoading();
        _isLoading.value = false;

        // Force rebuild by refreshing the user model
        userController.userModel.refresh();
      }
    });

    try {
      // Load user data
      await userController.getUserData();

      if (kDebugMode) {
        print("User data loaded successfully in HomePage");
        print("User ID: ${appController.userId}");
        print("User model: ${userController.userModel.value.toJson()}");
      }
    } catch (e) {
      if (kDebugMode) {
        print("Error loading user data in HomePage: $e");
      }
    } finally {
      // Ensure loading states are reset
      if (_isMounted) {
        userController.stopLoading();
        _isLoading.value = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        backgroundColor: AppColors.whitePure,


        // APPBAR ----------
        appBar: AppBar(
          automaticallyImplyLeading: false,
          toolbarHeight: 65,
          actions: [
            // SETTINGS ICON ----------
            InkWell(
              onTap: () async {
                // Navigate to settings and wait for result
                await Get.to(
                  () => const SettingsPage(),
                  binding: HomeBinding(),
                );

                // Reload data when returning from settings
                if (_isMounted) {
                  _loadUserData();
                }
              },
              child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: const BoxDecoration(
                      color: AppColors.whitePure, shape: BoxShape.circle),
                  child: SvgPicture.asset(
                    AppIcons.settingIcon,
                    width: 22,
                    height: 22,
                  )),
            ),
            const SizedBox(
              width: 17,
            ),
          ],
          title: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                  decoration: const BoxDecoration(
                      color: AppColors.whitePure, shape: BoxShape.circle),
                  child: ClipOval(
                    child: Image.asset(
                      AppIcons.userDefaultIcon,
                      width: 30,
                      height: 30,
                    ),
                  )),
              const SizedBox(
                width: 11.49,
              ),
              Expanded(
                  child: Text(
                appController.userId,
                style: styleWhiteLarge,
              ))
            ],
          ),
        ),
        // APPBAR ##########


        // BODY ----------
        body: Column(
          children: [
            Expanded(
              child: Obx(() {
                // Show loading indicator if loading
                if (_isLoading.value || userController.isLoading.value) {
                  return const Center(child: CustomProgressIndicator());
                }

                // Check if user ID is empty (might happen after account deletion)
                if (appController.userId.isEmpty) {
                  // Force navigation back to login if somehow we're on home page with no user ID
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (_isMounted) {
                      Get.back();
                    }
                  });
                  return const Center(child: CustomProgressIndicator());
                }

                // Show content based on user location
                if ((userController.userModel.value.userLocation?.country ??
                      "") == "Saudi Arabia" ||
                    (userController.userModel.value.userLocation?.country ??
                      "") == "المملكة العربية السعودية"
                    ) {
                  return Center(
                    child: Text(
                      'No record found'.tr,
                      textAlign: TextAlign.center,
                      style: stylePrimaryLarge,
                    ),
                  );
                } else {
                  return const LocationNotSupport();
                }
              }),
            ),
          ],
        ),
        // BODY ##########


      ),
    );
  }
}
