/// Location Not Supported Widget
///
/// Displays a message when the user's location is not supported
/// Used in the home page when location services are unavailable
library location_not_support;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Widget for displaying location not supported message
///
/// Shows an icon and explanatory text when the user's location
/// is not supported by the application
class LocationNotSupport extends StatelessWidget {
  /// Creates a LocationNotSupport widget
  const LocationNotSupport({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SvgPicture.asset(AppIcons.locationNotSupport),
        const SizedBox(
          height: 38,
        ),
        Text("Location not supported".tr,
            textAlign: TextAlign.center, style: stylePrimaryLarge),
        const SizedBox(
          height: 14,
        ),
        Text(
          "No one here right now".tr,
          textAlign: TextAlign.center,
          style: stylePrimaryNormal,
        ),
      ],
    );
  }
}
