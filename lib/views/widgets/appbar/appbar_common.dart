/// Common App Bar Widget
///
/// Provides a standardized app bar with consistent styling
/// Used throughout the application for navigation and page titles
library appbar_common;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import '../../../controllers/user_controller.dart';

/// Common App Bar Widget
///
/// A reusable app bar component with back button and title
/// Provides consistent styling with the app's design language
class AppbarCommon extends StatelessWidget implements PreferredSizeWidget {
  /// Title to display in the app bar
  final String title;

  /// Base AppBar to extend (for height calculation)
  final AppBar appbar;

  /// Current friend ID (used in dialogs)
  final String? currentFrndID;

  /// User controller for user-related operations
  final UserController? userController;

  /// Creates an AppbarCommon
  ///
  /// @param title Required title to display in the app bar
  /// @param appbar Required base AppBar to extend
  /// @param currentFrndID Optional friend ID for dialog display
  /// @param userController Optional controller for user operations
  const AppbarCommon(
      {super.key,
      required this.title,
      required this.appbar,
      this.currentFrndID,
      this.userController});

  @override
  Widget build(BuildContext context) {
    // APP BAR IMPLEMENTATION ----------
    return AppBar(
      // No shadow
      elevation: 0,

      // Primary color background
      backgroundColor: AppColors.primaryPurple,

      // Center the title
      centerTitle: true,

      // Don't automatically add a back button
      automaticallyImplyLeading: false,

      // Title with white text
      title: Text(
        title,
        style: styleBlackLarge.copyWith(color: AppColors.whitePure),
      ),

      // Custom back button that only appears when there's a page to go back to
      leading: Navigator.canPop(context)
          ? IconButton(
              icon: SvgPicture.asset(
                AppIcons.arrowBack,
                height: 17.5,
                width: 30,
              ),
              onPressed: () {
                // Handle navigation
                if (userController == null) {
                  Navigator.of(context).pop();
                  return;
                }
                Navigator.of(context).pop();
              },
            )
          : null,
    );
    // APP BAR IMPLEMENTATION ##########
  }

  /// Returns the preferred size of the app bar
  ///
  /// Required by PreferredSizeWidget interface
  /// Uses the standard toolbar height
  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  /// Shows a dialog with the current friend ID
  ///
  /// Used for displaying friend information
  ///
  /// @param context The BuildContext for showing the dialog
  void renameDialog(
    BuildContext context,
  ) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: AppColors.blackPure.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 0),
      pageBuilder: (_, __, ___) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: AppColors.whitePure,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 30,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 23),
                    child: Text(
                      currentFrndID ?? "",
                      textAlign: TextAlign.center,
                      style: styleBlackLarge,
                    ),
                  ),
                  const SizedBox(height: 40),
                  Container(
                    padding: const EdgeInsets.only(left: 16.6, right: 16.6),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            ///close dialog
                            Get.back();
                          },
                          child: Container(
                            height: 52,
                            width: MediaQuery.of(context).size.width * 0.35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                boxShadow: const [
                                  BoxShadow(
                                    color: AppColors.greyLightest,
                                    spreadRadius: 0.1,
                                    blurRadius: 0.1,
                                    offset: Offset(
                                        0, 0.5), // changes position of shadow
                                  ),
                                ],
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'Cancel'.tr,
                              style: styleGreyLarge,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        return FadeTransition(
          opacity: anim,
          child: child,
        );
      },
    );
  }

  /// Shows a block/unblock confirmation dialog
  ///
  /// Used for confirming user blocking or unblocking actions
  ///
  /// @param context The BuildContext for showing the dialog
  /// @param heading The dialog title
  /// @param text The dialog message
  /// @param isUnblock Whether this is an unblock action (true) or block action (false)
  void showBlockDialog(
      BuildContext context, String heading, String text, bool isUnblock) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: AppColors.blackPure.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 0),
      pageBuilder: (_, __, ___) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: AppColors.whitePure,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 26.56,
                  ),
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: AppColors.red.withOpacity(0.1),
                        shape: BoxShape.circle),
                    child: Container(
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        AppIcons.blockDialougeIcon,
                        width: 28,
                        height: 28,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 24,
                  ),
                  Text(
                    heading,
                    style: styleBlackLarge,
                  ),
                  const SizedBox(height: 21.88),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      text,
                      textAlign: TextAlign.center,
                      style: styleBlackLarge,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.only(left: 16.6, right: 16.6),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            ///close dialog
                            Get.back();
                          },
                          child: Container(
                            height: 52,
                            width: MediaQuery.of(context).size.width * 0.35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest.withOpacity(0.8),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.greyDark.withOpacity(0.2),
                                    spreadRadius: 0.1,
                                    blurRadius: 0.1,
                                    offset: const Offset(
                                        0, 0.5), // changes position of shadow
                                  ),
                                ],
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'Cancel'.tr,
                              style: styleBlackLarge,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        return FadeTransition(
          opacity: anim,
          child: child,
        );
      },
    );
  }
}
