import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/views/widgets/progress_indicator.dart';

/// Custom Loading Overlay
///
/// A customized loading overlay with square rounded corners, white background,
/// purple border, and gray shadow. This widget wraps around another widget
/// and displays a loading indicator when [isLoading] is true.
class CustomLoadingOverlay extends StatefulWidget {
  /// The child widget to display
  final Widget child;

  /// Whether to show the loading indicator
  final bool isLoading;

  /// The opacity of the modal barrier
  final double opacity;

  /// The progress indicator to show
  final Widget progressIndicator;

  /// The border radius of the loading container
  final double borderRadius;

  /// The border color of the loading container
  final Color borderColor;

  /// The background color of the loading container
  final Color backgroundColor;

  /// The shadow color of the loading container
  final Color shadowColor;

  /// The shadow elevation of the loading container
  final double elevation;

  /// Creates a CustomLoadingOverlay
  ///
  /// The [child] and [isLoading] parameters are required.
  const CustomLoadingOverlay({
    super.key,
    required this.child,
    required this.isLoading,
    this.opacity = 0.2,
    this.progressIndicator = const CustomProgressIndicator(),
    this.borderRadius = 12.0,
    this.borderColor = AppColors.primaryPurple,
    this.backgroundColor = AppColors.whitePure,
    this.shadowColor = AppColors.greyDark,
    this.elevation = 4.0,
  });

  @override
  State<CustomLoadingOverlay> createState() => _CustomLoadingOverlayState();
}

class _CustomLoadingOverlayState extends State<CustomLoadingOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _animation = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );
  }

  @override
  void didUpdateWidget(CustomLoadingOverlay oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isLoading != oldWidget.isLoading) {
      if (widget.isLoading) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Main content
        widget.child,

        // Loading overlay
        if (widget.isLoading)
          FadeTransition(
            opacity: _animation,
            child: Stack(
              children: [
                // Modal barrier
                ModalBarrier(
                  color: AppColors.blackPure.withOpacity(widget.opacity),
                  dismissible: false,
                ),

                // Centered loading container
                Center(
                  child: Material(
                    elevation: widget.elevation,
                    shadowColor: widget.shadowColor,
                    borderRadius: BorderRadius.circular(widget.borderRadius),
                    child: Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: widget.backgroundColor,
                        borderRadius: BorderRadius.circular(widget.borderRadius),
                        border: Border.all(
                          color: widget.borderColor,
                          width: 2.0,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: widget.shadowColor.withOpacity(0.3),
                            spreadRadius: 1,
                            blurRadius: 5,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Center(
                        child: widget.progressIndicator,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
