/// Custom Button Widget
///
/// Provides a standardized button with consistent styling
/// Used throughout the application for primary actions
library custom_button;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Custom Button Widget
///
/// A reusable button component with customizable appearance
/// Provides consistent styling with the app's design language
class CustomButton extends StatefulWidget {
  /// Text to display on the button
  final String text;

  /// Function to execute when the button is pressed
  final Function onPressed;

  /// Height of the button
  final double height;

  /// Background color of the button (defaults to primary color)
  final Color? bgColor;

  /// Text color of the button (defaults to white)
  final Color? textColor;
  /// Creates a CustomButton
  ///
  /// @param text Text to display on the button
  /// @param onPressed Function to execute when the button is pressed
  /// @param height Height of the button
  /// @param bgColor Optional background color (defaults to primary color)
  /// @param textColor Optional text color (defaults to white)
  const CustomButton(
      {super.key,
      required this.text,
      required this.onPressed,
      required this.height,
      this.bgColor,
      this.textColor});

  @override
  CustomButtonState createState() => CustomButtonState();
}

/// State class for CustomButton
class CustomButtonState extends State<CustomButton> {
  @override
  Widget build(BuildContext context) {
    // BUTTON LAYOUT ----------
    return SizedBox(
      // Set dimensions
      height: widget.height,
      width: double.infinity,

      // Button implementation
      child: ElevatedButton(
        // Handle button press
        onPressed: () => widget.onPressed(),

        // Button styling
        style: ElevatedButton.styleFrom(
            // Rounded corners
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            // Background color with fallback to primary color
            backgroundColor: widget.bgColor ?? AppColors.primaryPurple),

        // Button text
        child: Text(widget.text,
            style: styleWhiteLarge.copyWith(
              // Text color with fallback to white
              color: widget.textColor ?? AppColors.whitePure,
            )),
      ),
    );
    // BUTTON LAYOUT ##########
  }
}
