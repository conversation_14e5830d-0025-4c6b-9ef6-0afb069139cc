import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

class NewSnackBar {
  static SnackbarController newSnackbar(
    String title,
    String message,
    String type, {
    Color? colorText,
    Duration? duration = const Duration(seconds: 3),

    /// with instantInit = false you can put snackbar on initState
    bool instantInit = true,
    SnackPosition? snackPosition,
    Widget? titleText,
    Widget? messageText,
    Widget? icon,
    bool? shouldIconPulse,
    double? maxWidth,
    EdgeInsets? margin,
    EdgeInsets? padding,
    double? borderRadius,
    Color? red,
    double? borderWidth,
    Color? backgroundColor,
    Color? leftBarIndicatorColor,
    List<BoxShadow>? boxShadows,
    Gradient? backgroundGradient,
    TextButton? mainButton,
    OnTap? onTap,
    bool? isDismissible,
    bool? showProgressIndicator,
    DismissDirection? dismissDirection,
    AnimationController? progressIndicatorController,
    Color? progressIndicatorBackgroundColor,
    Animation<Color>? progressIndicatorValueColor,
    SnackStyle? snackStyle,
    Curve? forwardAnimationCurve,
    Curve? reverseAnimationCurve,
    Duration? animationDuration,
    double? barBlur,
    double? overlayBlur,
    SnackbarStatusCallback? snackbarStatus,
    Color? overlayColor,
    Form? userInputForm,
  }) {
    final getSnackBar = type == "2"
        ? GetSnackBar(
            snackbarStatus: snackbarStatus,
            title: title.toString().isNotEmpty ? title.toString() : "",
            titleText: Container(
              margin: const EdgeInsets.only(right: 10),
              child: titleText ??
                  Text(
                    title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: styleGreyNormal,
                  ),
            ),
            messageText: Container(
              margin: const EdgeInsets.only(right: 10),
              child: messageText ??
                  Text(
                    message,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: stylePrimaryNormal,
                  ),
            ),
            snackPosition: snackPosition ?? SnackPosition.TOP,
            borderRadius: borderRadius ?? 15,
            margin: margin ?? const EdgeInsets.symmetric(horizontal: 10),
            duration: duration,
            barBlur: barBlur ?? 7.0,
            backgroundColor: backgroundColor ?? AppColors.greyDark.withOpacity(0.2),
            icon: icon,
            shouldIconPulse: shouldIconPulse ?? true,
            maxWidth: maxWidth,
            padding: const EdgeInsets.all(10),
            borderColor: red,
            borderWidth: borderWidth,
            leftBarIndicatorColor: leftBarIndicatorColor,
            boxShadows: boxShadows,
            backgroundGradient: backgroundGradient,
            mainButton: TextButton(
              onPressed: () {
                Get.back();
              },
              child: SvgPicture.asset(
                AppIcons.msgCloseIcon,
                width: 24,
                height: 24,
              ),
            ),
            onTap: onTap,
            isDismissible: isDismissible ?? true,
            dismissDirection: dismissDirection,
            showProgressIndicator: showProgressIndicator ?? false,
            progressIndicatorController: progressIndicatorController,
            progressIndicatorBackgroundColor: progressIndicatorBackgroundColor,
            progressIndicatorValueColor: progressIndicatorValueColor,
            snackStyle: snackStyle ?? SnackStyle.FLOATING,
            forwardAnimationCurve: forwardAnimationCurve ?? Curves.easeOutCirc,
            reverseAnimationCurve: reverseAnimationCurve ?? Curves.easeOutCirc,
            animationDuration: animationDuration ?? const Duration(seconds: 1),
            overlayBlur: overlayBlur ?? 0.0,
            overlayColor: overlayColor ?? Colors.transparent,
            userInputForm: userInputForm)
        : GetSnackBar(
            snackbarStatus: snackbarStatus,
            messageText: Container(
              margin: const EdgeInsets.only(right: 10),
              child: messageText ??
                  Text(
                    title,
                    style: stylePrimaryNormal,
                  ),
            ),
            snackPosition: snackPosition ?? SnackPosition.TOP,
            borderRadius: borderRadius ?? 15,
            margin: margin ?? const EdgeInsets.symmetric(horizontal: 10),
            duration: duration,
            barBlur: barBlur ?? 7.0,
            backgroundColor: backgroundColor ?? AppColors.greyDark.withOpacity(0.2),
            icon: icon,
            shouldIconPulse: shouldIconPulse ?? true,
            maxWidth: maxWidth,
            padding: padding ?? const EdgeInsets.all(16),
            borderColor: red,
            borderWidth: borderWidth,
            leftBarIndicatorColor: leftBarIndicatorColor,
            boxShadows: boxShadows,
            backgroundGradient: backgroundGradient,
            mainButton: TextButton(
              onPressed: () {
                Get.back();
              },
              child: SvgPicture.asset(
                AppIcons.msgCloseIcon,
                width: 24,
                height: 24,
              ),
            ),
            onTap: onTap,
            isDismissible: isDismissible ?? true,
            dismissDirection: dismissDirection,
            showProgressIndicator: showProgressIndicator ?? false,
            progressIndicatorController: progressIndicatorController,
            progressIndicatorBackgroundColor: progressIndicatorBackgroundColor,
            progressIndicatorValueColor: progressIndicatorValueColor,
            snackStyle: snackStyle ?? SnackStyle.FLOATING,
            forwardAnimationCurve: forwardAnimationCurve ?? Curves.easeOutCirc,
            reverseAnimationCurve: reverseAnimationCurve ?? Curves.easeOutCirc,
            animationDuration: animationDuration ?? const Duration(seconds: 1),
            overlayBlur: overlayBlur ?? 0.0,
            overlayColor: overlayColor ?? Colors.transparent,
            userInputForm: userInputForm);

    final controller = SnackbarController(getSnackBar);

    if (instantInit) {
      controller.show();
    } else {
      //routing.isSnackbar = true;
      SchedulerBinding.instance.addPostFrameCallback((_) {
        controller.show();
      });
    }
    return controller;
  }
}
