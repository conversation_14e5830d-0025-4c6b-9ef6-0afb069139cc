/// Custom Phone Text Field Widget
///
/// Provides a specialized text input field for phone numbers
/// Includes phone icon and RTL text direction support
library phonefield_custom;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Custom Phone Text Field Widget
///
/// A specialized text input field for phone numbers with RTL support
/// Provides consistent styling with the app's design language
class PhoneTextFieldCustom extends StatefulWidget {
  /// Focus node for controlling focus
  final FocusNode? focusNode;

  /// Icon button to display in the text field
  final IconButton? icon;

  /// Custom prefix icon widget
  final Widget? prefixIcon;

  /// Hint text to display when the field is empty
  final String? hint;

  /// Label text to display above the field
  final String? label;

  /// Whether this is a password field (obscures text)
  final bool isPass;

  /// Whether the field is read-only
  final bool? readOnly;

  /// Controller for the text field
  final TextEditingController? controller;

  /// Validation function that returns an error message or null
  final String? Function(String?) validation;

  /// Function to call when the field is tapped (for read-only fields)
  final String? Function()? onTap;

  /// Keyboard type to use (e.g., email, number)
  final TextInputType? keyboardType;

  /// Input formatters to restrict or format input
  final List<TextInputFormatter>? inputFormatters;
  /// Creates a PhoneTextFieldCustom
  ///
  /// @param icon Optional icon button to display in the field
  /// @param hint Optional hint text to display when empty
  /// @param controller Optional controller for the text field
  /// @param focusNode Optional focus node for controlling focus
  /// @param validation Required validation function
  /// @param isPass Required flag for password field behavior
  /// @param label Optional label text to display above the field
  /// @param readOnly Optional flag for read-only behavior
  /// @param onTap Optional function to call when tapped (for read-only fields)
  /// @param keyboardType Optional keyboard type to use
  /// @param inputFormatters Optional input formatters to restrict input
  /// @param prefixIcon Optional custom prefix icon widget
  const PhoneTextFieldCustom(
      {super.key,
      this.icon,
      this.hint,
      this.controller,
      this.focusNode,
      required this.validation,
      required this.isPass,
      this.label,
      this.readOnly,
      this.onTap,
      this.keyboardType,
      this.inputFormatters,
      this.prefixIcon});
  @override
  PhoneTextFieldCustomState createState() => PhoneTextFieldCustomState();
}

/// State class for PhoneTextFieldCustom
class PhoneTextFieldCustomState extends State<PhoneTextFieldCustom> {
  /// Shorthand accessor for the validation function
  get validation => widget.validation;

  @override
  Widget build(BuildContext context) {
    // PHONE FIELD IMPLEMENTATION ----------
    return Container(
      // Horizontal margins
      // margin: const EdgeInsets.only(left: 27.5, right: 27.5),
      margin: const EdgeInsets.symmetric(horizontal: 43),
      // Force RTL text direction for Arabic support
      child: Directionality(
        textDirection: TextDirection.rtl,
        child: TextFormField(
          // Field configuration
          keyboardType: widget.keyboardType ?? TextInputType.text,
          onTap: widget.onTap ?? () {},
          readOnly: widget.readOnly ?? false,
          validator: validation,
          focusNode: widget.focusNode,
          controller: widget.controller,
          obscureText: widget.isPass,
          cursorColor: AppColors.primaryPurple,
          inputFormatters: widget.inputFormatters ?? [],
          textAlign: TextAlign.right,
          // Field decoration
          decoration: InputDecoration(
            // Label with RTL direction
            label: Directionality(
              textDirection: TextDirection.rtl,
              child: Text(
                widget.label.toString(),
                style: stylePrimaryNormal,
              ),
            ),

            // Consistent padding
            contentPadding: const EdgeInsets.all(17),

            // Default border
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  const BorderSide(color: AppColors.red, width: 2),
            ),

            // Focused border
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  const BorderSide(color: AppColors.primaryPurple, width: 2),
            ),

            // Error borders
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  const BorderSide(color: AppColors.red, width: 2),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide:
                  const BorderSide(color: AppColors.red, width: 2),
            ),

            // Commented out suffix icon - kept for reference
            // suffixIcon: widget.icon,

            // Phone icon prefix
            prefixIcon: Container(
              margin: const EdgeInsets.only(top: 11, bottom: 11),
              child: SvgPicture.asset(
                AppIcons.mobileLogin,
                height: 5,
                width: 10,
              ),
            ),

            // Hint text configuration
            hintTextDirection: TextDirection.rtl,
            hintText: widget.hint,
            hintStyle: const TextStyle(
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
    // PHONE FIELD IMPLEMENTATION ##########
  }
}
