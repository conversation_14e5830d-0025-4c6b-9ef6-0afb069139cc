import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/location_controller.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/views/widgets/appbar/appbar_common.dart';
import 'package:towasl/views/widgets/custom_button.dart';
import 'package:towasl/views/widgets/progress_indicator.dart';

/// Location Page
/// Handles user location selection and permission handling
/// Used both during initial setup and when editing profile
class LocationPage extends StatefulWidget {
  final bool? isFromEdit;
  const LocationPage({super.key, this.isFromEdit});

  @override
  State<LocationPage> createState() => _LocationPageState();
}

class _LocationPageState extends State<LocationPage> {
  /// Controller for handling location services and data
  LocationController locationController = Get.find<LocationController>();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Commented out to prevent automatic location fetching on page load
      // locationController.getCurrentLocation();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whitePure,
      // APPBAR ----------
      appBar: AppbarCommon(
        title: 'Location'.tr,
        appbar: AppBar(),
      ),
      // APPBAR ##########

      // BODY ----------
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Obx(() {
          return Column(
            children: [
              // Privacy description section
              SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 24.19,
                    ),
                    Container(
                      alignment: Alignment.center,
                      margin: const EdgeInsets.only(left: 13.91, right: 13.91),
                      child: Text(
                        'privacy_first_desc'.tr,
                        textAlign: TextAlign.center,
                        style: stylePrimaryLarge,
                      ),
                    ),
                  ],
                ),
              ),
              // Location status section - shows loading indicator or location icon
              Expanded(
                  child: locationController.isLoading.value
                      ? Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CustomProgressIndicator(),
                            const SizedBox(
                              height: 8,
                            ),
                            Text(
                              "fetching_location".tr,
                              textAlign: TextAlign.center,
                              style: styleGreyLarge,
                            ),
                          ],
                        )
                      : SvgPicture.asset(AppIcons.locationFetchIcon)),
              // Divider shown when app settings need to be opened
              locationController.isOpenAppSetting.value
                  ? Container(
                      color: AppColors.greyLight,
                      height: 1,
                      width: double.infinity,
                    )
                  : Container(),

              // Next button - only shown when not loading
              !locationController.isLoading.value
                  ? Container(
                      margin: const EdgeInsets.only(
                          left: 27.5, right: 27.5, top: 25, bottom: 25),
                      child: CustomButton(
                        text: 'next'.tr,
                        height: 46,
                        onPressed: () {
                          // Get current location and handle navigation based on context
                          locationController.getCurrentLocation(
                              isFromEdit: widget.isFromEdit ?? false,
                              isFromLocationPage: true);
                        },
                      ),
                    )
                  : Container(),
            ],
          );
        }),
      ),
      // BODY ##########
    );
  }
}
