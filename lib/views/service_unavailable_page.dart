/// Service Unavailable Page
///
/// Displayed when the application services are temporarily unavailable
/// Provides a user-friendly error message with an illustration
library service_unavailable_page;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Service Unavailable Screen
///
/// Displayed when the application services are temporarily unavailable
/// Shows an illustration and apologetic message to the user
class ServiceUnavailablePage extends StatefulWidget {
  /// Creates a ServiceUnavailablePage
  const ServiceUnavailablePage({super.key});

  @override
  State<ServiceUnavailablePage> createState() => _ServiceUnavailablePageState();
}

/// State for the ServiceUnavailablePage
class _ServiceUnavailablePageState extends State<ServiceUnavailablePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: SingleChildScrollView(
          child: Container(
            padding: const EdgeInsets.only(left: 29, right: 29),
            child: Column(
              children: [
                SvgPicture.asset(AppIcons.serviceUnavailIcon),
                const SizedBox(
                  height: 33,
                ),
                Text(
                  "Service Temporarily Unavailable",
                  textAlign: TextAlign.center,
                  style: stylePrimaryLarge,
                ),
                const SizedBox(
                  height: 15,
                ),
                Text(
                  "service_apology".tr,
                  textAlign: TextAlign.center,
                  style: stylePrimaryNormal,
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
