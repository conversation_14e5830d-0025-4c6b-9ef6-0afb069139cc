import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/views/widgets/custom_loading_overlay.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/interests_controller.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/views/widgets/appbar/appbar_common.dart';
import 'package:towasl/views/widgets/custom_button.dart';
import 'package:towasl/views/widgets/expanded_tile_custom.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';
import 'widgets/progress_indicator.dart';

/// Interests Page
/// Allows users to select their interests from predefined categories
/// Used both during initial setup and when editing profile settings
class InterestsPage extends StatefulWidget {
  final bool? isFromSetting;
  const InterestsPage({super.key, this.isFromSetting});
  @override
  InterestsPageState createState() => InterestsPageState();
}

class InterestsPageState extends State<InterestsPage> {
  /// Controller for handling interest data and operations
  late InterestsController interestsController;

  /// App controller for global state
  final AppController appController = Get.find<AppController>();

  /// Controller for expandable interest category tiles
  late ExpandedTileControllerCustom _controller;

  @override
  void initState() {
    /// Initialize expandable tile controller
    _controller = ExpandedTileControllerCustom(isExpanded: true);

    /// Get the interests controller
    interestsController = Get.find<InterestsController>();

    /// Fetch interest data after widget is built
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      /// Get interest data with context flag (from settings or initial setup)
      interestsController.getInterestData(widget.isFromSetting ?? false);
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whitePure,
      // APPBAR ----------
      appBar: AppbarCommon(
        title: 'select_your_interests'.tr,
        appbar: AppBar(),
      ),
      // APPBAR ##########

      // BODY ----------
      body: Directionality(
        textDirection: TextDirection.rtl,
        child: Obx(() {
          // Always use CustomLoadingOverlay to ensure it shows during both initial loading and saving
          return CustomLoadingOverlay(
            opacity: 0.2,
            isLoading: interestsController.isLoading.value,
            progressIndicator: const CustomProgressIndicator(),
            child: interestsController.interestData.isEmpty &&
                    interestsController.isLoading.value
                ? const Center(
                    child:
                        CustomProgressIndicator()) // Show progress indicator during initial loading
                : Column(
                    children: [
                      // Interest categories list with expandable tiles
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.only(
                              left: 17.22,
                              right: 17.22,
                              top: 19.07,
                              bottom: 10),
                          child: interestsController.interestData.isEmpty
                              ? Center(
                                  child: Text(
                                    'No record found'.tr,
                                    style: styleGreyNormal,
                                  ),
                                )
                              : ExpandedTileList.separated(
                                  itemCount:
                                      interestsController.interestData.length,
                                  maxOpened:
                                      4, // Maximum number of tiles that can be open at once
                                  reverse: false,
                                  itemBuilder: (context, index, controller) {
                                    return ExpandedTileCustom(
                                      contentseparator: 0,
                                      trailingRotation: 180,
                                      trailing: SvgPicture.asset(
                                        AppIcons.arrowDown,
                                        height: 10.5,
                                        width: 9,
                                      ),
                                      theme: const ExpandedTileThemeDataCustom(
                                        headerSplashColor: Colors.transparent,
                                        headerColor: AppColors.whitePure,
                                        contentBackgroundColor:
                                            AppColors.whitePure,
                                      ),
                                      controller: _controller,

                                      // Category heading
                                      title: Text(
                                          interestsController
                                              .interestData[index].category
                                              .toString(),
                                          style: stylePrimaryLarge.copyWith(
                                              fontWeight: _controller.isExpanded
                                                  ? FontWeight.w800
                                                  : FontWeight.w600)),
                                      // Subcategories content - displayed when tile is expanded
                                      content: Container(
                                        margin: const EdgeInsets.only(
                                            top: 3, bottom: 4),
                                        child: Wrap(
                                            direction: Axis.horizontal,
                                            alignment: WrapAlignment.start,
                                            children: List.generate(
                                              interestsController
                                                  .interestData[index]
                                                  .subCategories!
                                                  .length,
                                              (newIndex) => InkWell(
                                                onTap: () {
                                                  // Toggle selection state of subcategory
                                                  interestsController
                                                          .interestData[index]
                                                          .subCategories![newIndex]
                                                          .isSelected =
                                                      (!(interestsController
                                                          .interestData[index]
                                                          .subCategories![
                                                              newIndex]
                                                          .isSelected)!);
                                                  // Refresh to update UI
                                                  interestsController
                                                      .interestData
                                                      .refresh();
                                                },
                                                // Subcategory selectable item
                                                child: Container(
                                                  margin: const EdgeInsets.only(
                                                      left: 5.28,
                                                      right: 5.28,
                                                      bottom: 14),
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 7.5,
                                                          bottom: 7.5,
                                                          left: 8,
                                                          right: 8),
                                                  // Change appearance based on selection state
                                                  decoration: BoxDecoration(
                                                      color: (interestsController
                                                                  .interestData[
                                                                      index]
                                                                  .subCategories![
                                                                      newIndex]
                                                                  .isSelected ??
                                                              false)
                                                          ? AppColors
                                                              .green // Selected
                                                          : AppColors
                                                              .whitePure, // Unselected
                                                      border: Border.all(
                                                          color: (interestsController
                                                                      .interestData[
                                                                          index]
                                                                      .subCategories![
                                                                          newIndex]
                                                                      .isSelected ??
                                                                  false)
                                                              ? Colors
                                                                  .transparent
                                                              : AppColors
                                                                  .greyMedium),
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              8)),

                                                  // Subcategory name
                                                  child: Text(
                                                    interestsController
                                                        .interestData[index]
                                                        .subCategories![
                                                            newIndex]
                                                        .subcategory
                                                        .toString(),
                                                    textAlign: TextAlign.center,
                                                    style: styleGreyNormal
                                                        .copyWith(
                                                      color: (interestsController
                                                                  .interestData[
                                                                      index]
                                                                  .subCategories![
                                                                      newIndex]
                                                                  .isSelected ??
                                                              false)
                                                          ? AppColors.whitePure
                                                          : AppColors.greyDark,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            )),
                                      ),
                                    );
                                  },
                                  separatorBuilder:
                                      (BuildContext context, int index) {
                                    return const SizedBox(
                                      height: 25,
                                    );
                                  },
                                ),
                        ),
                      ),
                      // Divider before buttons
                      Container(
                        color: AppColors.greyLight,
                        height: 1,
                        width: double.infinity,
                      ),

                      // Different button layouts based on context (settings or initial setup)
                      (widget.isFromSetting ?? false)
                          // Settings mode: Show Cancel and Save buttons
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Cancel button
                                InkWell(
                                  onTap: () {
                                    Get.back();
                                  },
                                  child: Container(
                                    height: 52,
                                    margin: const EdgeInsets.only(
                                        right: 18, top: 25, bottom: 25),
                                    width: MediaQuery.of(context).size.width *
                                        0.35,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                        color: AppColors.whitePure,
                                        borderRadius:
                                            BorderRadius.circular(10)),
                                    child: Text(
                                      'Cancel'.tr,
                                      style: styleGreyLarge,
                                    ),
                                  ),
                                ),
                                // Save button
                                Container(
                                  width:
                                      MediaQuery.of(context).size.width * 0.4,
                                  margin: const EdgeInsets.only(
                                      left: 18, top: 25, bottom: 25),
                                  child: CustomButton(
                                    text: 'Save'.tr,
                                    height: 52,
                                    onPressed: () {
                                      // Save interests and return to settings
                                      interestsController.setInterestData(
                                          interestsController.interestData,
                                          isFromSetting: true);
                                    },
                                  ),
                                ),
                              ],
                            )
                          // Initial setup mode: Show Next button
                          : Container(
                              margin: const EdgeInsets.only(
                                  left: 27.5, right: 27.5, top: 25, bottom: 25),
                              child: CustomButton(
                                text: 'next'.tr,
                                height: 46,
                                onPressed: () {
                                  if (appController.userId.isEmpty) {
                                    // User not logged in
                                    ToastCustom.errorToast('login_first'.tr);
                                  } else {
                                    // Save interests and proceed to next step
                                    interestsController.setInterestData(
                                        interestsController.interestData);
                                  }
                                },
                              ),
                            ),
                    ],
                  ),
          );
        }),
      ),
      // BODY ##########
    );
  }
}
