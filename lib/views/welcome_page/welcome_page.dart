/// Terms and Conditions Acceptance Page
///
/// This page displays the app's key features and policies
/// Users must accept terms before proceeding to login
library welcome_page;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/signup_login_binding.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/views/signup_login_page/signup_login_page.dart';
import 'package:towasl/views/widgets/custom_button.dart';
import 'widget/terms_desc_widget.dart';

/// Terms and Conditions Acceptance Screen
///
/// Displays the app's key features and policies that users must accept
/// before proceeding to the login screen
class WelcomePage extends StatefulWidget {
  /// Creates an WelcomePage
  const WelcomePage({super.key});

  @override
  State<WelcomePage> createState() => _WelcomePageState();
}

/// State for the WelcomePage
class _WelcomePageState extends State<WelcomePage> {
  // No longer need to track checkbox state
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [



            // FEATURES DESCRIPTIONS & RULES ----------
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 60,
                    ),

                    // APP LOGO ----------
                    SvgPicture.asset(
                      AppIcons.splashLogo,
                      height: 75, // Increased logo height
                      width: 170, // Increased logo width proportionally
                    ),
                    const SizedBox(
                      height: 60, // Slightly increased spacing after logo
                    ),
                    // APP LOGO ##########

                    TermsDescWidget(
                      icon: '🌟',
                      title: 'Explore suitable friends'.tr,
                      desc:
                          'Same gender | Same interests | Nearby locations'.tr,
                    ),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                    customDivider(),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                    TermsDescWidget(
                      icon: '🎯',
                      title: 'To share enjoyable activities for a noble purpose'
                          .tr,
                      desc:
                          'The goal of the app is noble and aims to build genuine friendships.'
                              .tr,
                    ),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                    customDivider(),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                    TermsDescWidget(
                      icon: '🔒',
                      title: 'With high privacy'.tr,
                      desc:
                          'We commit to protecting your privacy by not displaying your name, location, age, or phone number to any other user.'
                              .tr,
                    ),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                    customDivider(),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                    TermsDescWidget(
                      icon: '🚫',
                      title: "Adhering to the app's purpose is a red line".tr,
                      desc:
                          'Unethical use may result in legal action and permanent account ban from the app.'
                              .tr,
                    ),
                    const SizedBox(
                      height: 24, // Increased vertical spacing
                    ),
                  ],
                ),
              ),
            ),
            // FEATURES DESCRIPTIONS & RULES ##########



            // Terms and Conditions checkbox moved to signup_login_page.dart



            // LET'S START BUTTON ----------
            // Divider with consistent padding
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 35),
              child: Divider(
                color: AppColors.whiteLightGray,
                height: 1,
              ),
            ),
            const SizedBox(
              height: 31,
            ),
            // Button with consistent padding
            Container(
              margin: const EdgeInsets.only(left: 35, right: 35, bottom: 20),
              child: Directionality(
                textDirection: TextDirection.rtl,
                child: CustomButton(
                  text: "Let's start!".tr,
                  height: 50, // Slightly taller button
                  onPressed: () {
                    // Navigate directly to the SignupLoginPage
                    Get.offAll(() => const SignupLoginPage(),
                        binding: SignupLoginBinding());
                  },
                ),
              ),
            ),
            // LET'S START BUTTON ##########


          ],
        ),
      ),
    );
  }

  /// Creates a custom divider with consistent styling
  ///
  /// Used to separate the different sections of the terms page
  Widget customDivider() {
    return const Padding(
        // Updated padding to match the TermsDescWidget
        padding: EdgeInsets.only(left: 35, right: 35),
        child: Divider(
          color: AppColors.whiteLightGray,
          height: 1,
        ));
  }
}
