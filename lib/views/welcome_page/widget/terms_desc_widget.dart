/// Terms Description Widget
///
/// A reusable widget for displaying terms and conditions items
/// with an emoji icon, title, and description
library terms_desc_widget;

import 'package:flutter/material.dart';
import 'package:towasl/core/theme/app_text_styles.dart';

/// Widget for displaying a terms and conditions item
///
/// Displays an emoji icon, title, and description text in a consistent format
/// Used in the WelcomePage to display app policies and features
class TermsDescWidget extends StatelessWidget {
  /// Emoji icon to display (e.g., "🔒", "🚫")
  final String icon;

  /// Title text for the terms item
  final String title;

  /// Description text explaining the terms item
  final String desc;

  /// Creates a TermsDescWidget
  ///
  /// @param icon Emoji icon to display
  /// @param title Title text for the terms item
  /// @param desc Description text explaining the terms item
  const TermsDescWidget(
      {super.key, required this.icon, required this.title, required this.desc});

  @override
  Widget build(BuildContext context) {
    return Padding(
      // Further increased horizontal padding for the parent column
      padding: const EdgeInsets.only(left: 35, right: 35),
      child: Row(
        // Make icons top-aligned
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            icon,
            // Increased icon size from 26 to 32
            style: const TextStyle(fontSize: 32),
          ),
          const SizedBox(
            width: 16, // Increased spacing between icon and text
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  // Using a larger font size (16px) for the title
                  style: styleFushiNormal,
                ),
                Padding(
                  padding: const EdgeInsets.only(top: 5),
                  child: Text(
                    desc,
                    style: stylePrimaryNormal,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
