/// Settings Page
///
/// Allows users to view and edit their profile information
/// Provides options for managing interests and logging out
library settings_page;

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/bindings/interests_binding.dart';
import 'package:towasl/bindings/personal_info_binding.dart';
import 'package:towasl/bindings/signup_login_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/views/personal_info_page/personal_info_page.dart';
import 'package:towasl/views/signup_login_page/signup_login_page.dart';
import 'package:towasl/views/interests_page.dart';
import 'package:towasl/views/widgets/appbar/appbar_common.dart';
import '../core/theme/app_text_styles.dart';

/// Settings Screen
///
/// Displays user profile information and settings options
/// Allows navigation to profile editing, interests, and logout
class SettingsPage extends StatefulWidget {
  /// Creates a SettingsPage
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

/// State for the SettingsPage
class _SettingsPageState extends State<SettingsPage> {
  /// Controller for user-related operations
  late UserController userController;

  /// App controller for global state
  late AppController appController;

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    appController = Get.find<AppController>();
    userController = Get.find<UserController>();

    // Load user data after the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      userController.getUserData();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whitePure,
      appBar: AppbarCommon(
        title: 'Settings'.tr,
        appbar: AppBar(),
        userController: userController,
      ),
      body: Obx(() {
        return SingleChildScrollView(
          child: Column(
            children: [
              const SizedBox(
                height: 12.42,
              ),
              Container(
                alignment: Alignment.center,
                decoration: const BoxDecoration(shape: BoxShape.circle),
                child: ClipOval(
                  child: Image.asset(
                    AppIcons.userDefaultIcon,
                    width: 65,
                    height: 65,
                  ),
                ),
              ),
              const SizedBox(
                height: 4.84,
              ),

              // USER NAME ----------
              Text(
                userController.userModel.value.name.toString(),
                style: styleBlackLarge,
              ),
              const SizedBox(
                height: 4.84,
              ),

              // USER ID ----------
              Text(
                "${"ID:".tr} ${userController.userModel.value.userId ?? ""}",
                style: styleBlackNormal,
              ),
              const SizedBox(
                height: 4.84,
              ),

              // EDIT PROFILE BUTTON ----------
              InkWell(
                onTap: () {
                  Get.to(
                    () => const PersonalInfoPage(isEditing: true),
                    binding: PersonalInfoBinding(),
                  );
                },
                child: Container(
                  alignment: Alignment.center,
                  width: 220,
                  padding: const EdgeInsets.only(
                      left: 7.23, right: 7.23, top: 9, bottom: 9),
                  decoration: BoxDecoration(
                      color: AppColors.primaryPurple,
                      borderRadius: BorderRadius.circular(100)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Edit profile".tr,
                        style: styleWhiteLarge,
                      ),
                      const SizedBox(
                        width: 13.81,
                      ),
                      SvgPicture.asset(
                        AppIcons.profileArrowIcon,
                        width: 20,
                        height: 20,
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(
                height: 33.03,
              ),
              _profileItem(AppIcons.interestIcon, 'Interests'.tr, false, true),
              _profileItem(AppIcons.logoutIcon, 'Logout'.tr, true, false),
            ],
          ),
        );
      }),
    );
  }

  /// Creates a profile menu item
  ///
  /// @param icon Icon asset path to display
  /// @param title Text to display for the item
  /// @param isLogout Whether this is a logout item (affects styling)
  /// @param isContent Whether this item has additional content
  Widget _profileItem(
      String icon, String title, bool isLogout, bool isContent) {
    return InkWell(
      onTap: () {
        if (isLogout) {
          // Open logout confirmation dialog
          _showCustomDialog(context, 0, 'Logout'.tr, 'sure_logout'.tr);
        }
        if (title == 'Interests'.tr) {
          Get.to(
            () => const InterestsPage(
              isFromSetting: true,
            ),
            binding: InterestsBinding(),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.only(top: 22, bottom: 22),
        width: double.infinity,
        margin: const EdgeInsets.only(left: 18, right: 18),
        child: Row(
//        mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            SvgPicture.asset(
              icon,
              colorFilter: ColorFilter.mode(
                isLogout ? AppColors.red : AppColors.blackPure,
                BlendMode.srcIn,
              ),
              width: 24,
              height: 24,
            ),
            const SizedBox(
              width: 17.11,
            ),
            Text(
              title,
              style: styleBlackLarge.copyWith(
                  color: isContent
                      ? AppColors.blackCharcoal
                      : isLogout
                          ? AppColors.red
                          : AppColors.blackPure),
            ),
            isLogout
                ? Container()
                : Expanded(
                    child: Container(
                      alignment: Alignment.centerLeft,
                      child: SvgPicture.asset(
                        AppIcons.profileArrowIcon,
                        colorFilter: const ColorFilter.mode(
                          AppColors.blackCharcoal,
                          BlendMode.srcIn,
                        ),
                        width: 20,
                        height: 20,
                      ),
                    ),
                  )
          ],
        ),
      ),
    );
  }

  /// Shows a logout confirmation dialog
  ///
  /// @param context The BuildContext for showing the dialog
  /// @param index Index parameter (not used)
  /// @param heading Dialog title text
  /// @param text Dialog message text
  void _showCustomDialog(
      BuildContext context, int index, String heading, String text) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: AppColors.blackPure.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 0),
      pageBuilder: (_, __, ___) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: AppColors.whitePure,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 26.56,
                  ),
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: AppColors.red.withOpacity(0.1),
                        shape: BoxShape.circle),
                    child: Container(
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        AppIcons.logoutIcon,
                        width: 28,
                        height: 28,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 8.72,
                  ),
                  Text(
                    heading,
                    style: styleBlackLarge,
                  ),
                  const SizedBox(height: 21.88),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Text(
                      text,
                      textAlign: TextAlign.center,
                      style: styleBlackLarge,
                    ),
                  ),
                  const SizedBox(height: 40),
                  Container(
                    padding: const EdgeInsets.only(left: 16.6, right: 16.6),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            // Close dialog without action
                            Get.back();
                          },
                          child: Container(
                            height: 52,
                            width: MediaQuery.of(context).size.width * 0.35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.greyDark.withOpacity(0.2),
                                    spreadRadius: 0.1,
                                    blurRadius: 0.1,
                                    offset: const Offset(
                                        0, 0.5), // changes position of shadow
                                  ),
                                ],
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'Cancel'.tr,
                              style: styleBlackLarge,
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () async {
                            Get.back();

                            // Clear user data from AppController
                            appController.clearUserData();

                            // Clear user data from local storage
                            final storageService = Get.find<StorageService>();
                            await storageService.clearUserData();
                            Get.offAll(() => const SignupLoginPage(),
                                binding: SignupLoginBinding());
                          },
                          child: Container(
                            height: 52,
                            width: MediaQuery.of(context).size.width * 0.35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: AppColors.red,
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'Logout'.tr,
                              style: styleWhiteLarge,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        return FadeTransition(
          opacity: anim,
          child: child,
        );
      },
    );
  }
}
