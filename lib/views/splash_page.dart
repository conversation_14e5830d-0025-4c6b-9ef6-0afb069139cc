import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:towasl/bindings/home_binding.dart';
import 'package:towasl/bindings/interests_binding.dart';
import 'package:towasl/bindings/location_binding.dart';
import 'package:towasl/bindings/personal_info_binding.dart';
import 'package:towasl/controllers/app_controller.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/core/services/storage_service.dart';
import 'package:towasl/core/theme/app_animation.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/views/home_page/home_page.dart';
import 'package:towasl/views/interests_page.dart';
import 'package:towasl/views/location_page.dart';
import 'package:towasl/views/personal_info_page/personal_info_page.dart';
import 'package:towasl/views/welcome_page/welcome_page.dart';

/// Splash Page
/// Entry point of the application that displays the app logo
/// Handles authentication status check and navigation to appropriate screens
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  @override
  void initState() {
    super.initState();
    // Wait for splash animation to complete before checking user status
    Future.delayed(const Duration(milliseconds: 1300)).then((value) {
      checkStatus();
    });
  }

  /// Check logged in status and user data to determine the next screen
  ///
  /// Flow:
  /// 1. Check if user is logged in
  /// 2. If logged in, check if user has completed all required profile steps
  /// 3. Navigate to the appropriate screen based on completion status
  /// 4. If not logged in, navigate to terms acceptance screen
  checkStatus() async {
    // Get services and controllers
    StorageService storageService = Get.find<StorageService>();
    bool isLoggedIn = storageService.getLoggedInStatus();

    // Get the app controller
    AppController appController = Get.find<AppController>();

    if (isLoggedIn) {
      // User is logged in, get user ID from storage
      String userId = storageService.getUserIDValue();
      appController.userId = userId;

      // Initialize user controller with proper dependencies
      UserController userController = Get.find<UserController>();
      await userController.getUserData();

      // Check user profile completion status and navigate accordingly
      if (userController.userModel.value.userInterest == null) {
        // User needs to select interests
        Get.offAll(() => const InterestsPage(), binding: InterestsBinding());
      } else if (userController.userModel.value.userLocation == null) {
        // User needs to set location
        Get.offAll(() => const LocationPage(), binding: LocationBinding());
      } else if ((userController.userModel.value.nationality ?? "") == '') {
        // User needs to complete personal information
        Get.offAll(() => const PersonalInfoPage(), binding: PersonalInfoBinding());
      } else {
        // User profile is complete, go to home page
        Get.offAll(() => const HomePage(), binding: HomeBinding());
      }
    } else {
      // User is not logged in, clear app controller state and go to terms page
      appController.clearUserData();
      Get.offAll(const WelcomePage());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whiteIvory,
      // BODY ----------
      body: SafeArea(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Animated splash logo
                  Container(
                    alignment: Alignment.center,
                    child: Lottie.asset(
                      AppAnimation.splashLogo,
                      repeat: false,
                      height: 130,
                      width: double.infinity,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      // BODY ##########
    );
  }
}
