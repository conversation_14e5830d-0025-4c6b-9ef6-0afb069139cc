/// Login Page
///
/// Provides mobile login options for user authentication
/// Entry point for user authentication flow
library signup_login_page;

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/auth_controllers/mobile_login_controller.dart';
import 'package:towasl/controllers/auth_controllers/save_register_data_controller.dart';
import 'package:towasl/core/constants.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/utils/storage_service.dart';
import 'package:towasl/views/widgets/custom_icon_button.dart';
import 'package:towasl/views/widgets/progress_indicator.dart';
import 'package:towasl/views/widgets/textfield_custom/phonefield_custom.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';
import 'package:towasl/views/widgets/custom_loading_overlay.dart';

/// Login Screen
///
/// Provides mobile login options for user authentication
/// First screen in the authentication flow
class SignupLoginPage extends StatefulWidget {
  /// Creates a SignupLoginPage
  const SignupLoginPage({super.key});

  @override
  State<SignupLoginPage> createState() => _SignupLoginPageState();
}

/// State for the SignupLoginPage
class _SignupLoginPageState extends State<SignupLoginPage> {
  /// Controller for mobile login operations
  final MobileLoginController mobileLoginController = Get.find();

  /// Controller for saving registration data
  final SaveRegisterDataController saveRegisterDataController = Get.find();

  /// Controller for the mobile number field
  final TextEditingController _mobileController = TextEditingController();

  /// Focus node for the mobile field
  final FocusNode _mobileFocusNode = FocusNode();

  /// Tracks whether the user has accepted the terms and conditions
  bool _isChecked = false;

  @override
  void initState() {
    super.initState();
    // Load saved terms acceptance state
    _loadTermsAcceptedState();

    // Ensure the terms acceptance state is properly saved
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // This ensures the UI is fully built before we try to save the state
      await StorageService.setTermsAccepted(_isChecked);
    });
  }

  /// Loads the saved terms acceptance state from storage
  void _loadTermsAcceptedState() {
    bool termsAccepted = StorageService.getTermsAccepted();
    if (kDebugMode) {
      print('Loading terms acceptance state: $termsAccepted');
    }
    setState(() {
      _isChecked = termsAccepted;
    });
  }

  @override
  void dispose() {
    // Clean up the controller and focus node when the widget is disposed
    _mobileController.dispose();
    _mobileFocusNode.dispose();
    super.dispose();
  }

  /// Validates the mobile number format
  String? _validateMobile(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a mobile number'.tr;
    }

    // Saudi mobile number validation (starts with 05 followed by 8 digits)
    RegExp regex = RegExp(r"^05[0-9]{8}$");
    if (!regex.hasMatch(value)) {
      return 'Please enter a valid mobile number'.tr;
    }

    return null;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Unfocus the text field when tapping anywhere on the screen
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: AppColors.whitePure,
        extendBody: true,
        body: Obx(
          () => CustomLoadingOverlay(
            opacity: 0.2,
            isLoading: saveRegisterDataController.isLoading.value,
            progressIndicator: const CustomProgressIndicator(),
            child: SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),

                      // APP LOGO ----------
                      SvgPicture.asset(
                        AppIcons.splashLogo,
                        height: 72.97,
                        width: 179.36,
                      ),
                      const SizedBox(height: 17),

                      // APP SLOGAN ----------
                      Container(
                        margin: const EdgeInsets.only(left: 51, right: 51),
                        child: Text(
                          'slogan'.tr,
                          textAlign: TextAlign.center,
                          style: stylePrimaryNormal,
                        ),
                      ),
                      const SizedBox(height: 77.42),

                      // MOBILE FIELD ----------
                      PhoneTextFieldCustom(
                        controller: _mobileController,
                        focusNode: _mobileFocusNode,
                        label: 'Mobile'.tr,
                        hint: '05xxxxxxxx',
                        isPass: false,
                        keyboardType: TextInputType.phone,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(10),
                        ],
                        validation: _validateMobile,
                      ),

                      const SizedBox(height: 12),

                      // TERMS AND CONDITIONS CHECKBOX ----------
                      Container(
                        // Add a container with padding and decoration for better visual separation
                        margin: const EdgeInsets.symmetric(horizontal: 33),
                        // decoration: BoxDecoration(
                        //   color: AppColors.whitePure,
                        //   borderRadius: BorderRadius.circular(8),
                        //   border: Border.all(
                        //     color: AppColors.greyColor7,
                        //     width: 1,
                        //   ),
                        // ),
                        child: Directionality(
                          // Set text direction to right-to-left
                          textDirection: TextDirection.rtl,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment
                                .center, // Center align for better appearance
                            children: [
                              // Custom styled checkbox
                              Transform.scale(
                                scale: 1.1, // Slightly larger checkbox
                                child: Checkbox(
                                  materialTapTargetSize:
                                      MaterialTapTargetSize.shrinkWrap,
                                  activeColor: AppColors.primaryFushi,
                                  checkColor:
                                      AppColors.whitePure, // White checkmark
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(
                                        4), // Rounded corners
                                  ),
                                  side: const BorderSide(
                                    color: AppColors.greyDark,
                                    width: 1.5, // Slightly thicker border
                                  ),
                                  value: _isChecked,
                                  onChanged: (bool? value) async {
                                    // Update the checkbox state
                                    setState(() {
                                      _isChecked = value!;
                                    });

                                    // Save the terms acceptance state immediately
                                    // This is outside setState to ensure it's not affected by state rebuilds
                                    await StorageService.setTermsAccepted(
                                        _isChecked);

                                    if (kDebugMode) {
                                      print(
                                          'Saving terms acceptance state: $_isChecked');
                                      // Verify the save by reading it back
                                      Future.delayed(
                                          const Duration(milliseconds: 0), () {
                                        bool savedValue =
                                            StorageService.getTermsAccepted();
                                        if (kDebugMode) {
                                          print(
                                              'Verification - saved value: $savedValue');
                                        }
                                      });
                                    }
                                  },
                                ),
                              ),
                              const SizedBox(
                                  width:
                                      3), // More space between checkbox and text
                              Expanded(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      top:
                                          2), // Slight adjustment for vertical alignment
                                  child: RichText(
                                    textAlign:
                                        TextAlign.right, // Right-align the text
                                    text: TextSpan(
                                      text: "${'I agree to'.tr} ",
                                      style: stylePrimaryNormal,
                                      children: <TextSpan>[
                                        TextSpan(
                                          text: 'terms and conditions'.tr,
                                          style: styleFushiNormal,
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () {
                                              Constants.launchURL(
                                                  Constants.termsUrl);
                                            },
                                        ),
                                        TextSpan(
                                          text: " ${'and'.tr} ",
                                          style: stylePrimaryNormal,
                                        ),
                                        TextSpan(
                                          text: 'privacy policy'.tr,
                                          style: styleFushiNormal,
                                          recognizer: TapGestureRecognizer()
                                            ..onTap = () {
                                              Constants.launchURL(
                                                  Constants.privacyUrl);
                                            },
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24), // Spacing after checkbox
                      // TERMS AND CONDITIONS CHECKBOX ##########

                      // MOBILE LOGIN BUTTON ----------
                      Container(
                        margin: EdgeInsets.only(
                            left: 60, right: 60, top: Platform.isIOS ? 16 : 0),
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: CustomIconButton(
                            isBorder: true,
                            text: 'Continue with Mobile'.tr,
                            bgColor: AppColors.whitePure,
                            textColor: AppColors.greyDark,
                            icon: AppIcons.mobileLogin,
                            height: 42,
                            onPressed: () {
                              // Validate mobile number
                              String? validationError =
                                  _validateMobile(_mobileController.text);
                              if (validationError != null) {
                                ToastCustom.warningToastCustom(
                                    validationError, context, true);
                                return;
                              }

                              // Check terms acceptance
                              if (!_isChecked) {
                                ToastCustom.warningToastCustom(
                                    'Please agree to terms and conditions and privacy policy first'
                                        .tr,
                                    context,
                                    true);
                              } else {
                                // Proceed with mobile login
                                mobileLoginController.signInWithMobile(
                                  mobileNumber: _mobileController.text,
                                  context: context,
                                );
                              }
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 38.01),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
