import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/user_controller.dart';
import 'package:towasl/controllers/user/user_data_controller.dart';
import 'package:towasl/controllers/user/user_form_controller.dart';
import 'package:towasl/controllers/user/user_session_controller.dart';
import 'package:towasl/controllers/location_controller.dart';
import 'package:towasl/core/constants.dart';
import 'package:towasl/core/helpers/profile_validation.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_icons.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/core/helpers/custom_countries.dart';
import 'package:towasl/views/location_page.dart';
import 'package:towasl/views/personal_info_page/widgets/nationality_picker_dialog_custom.dart';
import 'package:towasl/views/widgets/appbar/appbar_common.dart';
import 'package:towasl/views/widgets/custom_button.dart';
import 'package:towasl/views/widgets/custom_loading_overlay.dart';
import 'package:towasl/views/widgets/progress_indicator.dart';
import 'package:towasl/views/widgets/textfield_custom/textfield_custom.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// Personal Information Page
/// Handles user profile creation and editing
/// Collects nationality, birth year, and gender information
class PersonalInfoPage extends StatefulWidget {
  final bool isEditing;
  const PersonalInfoPage({super.key, this.isEditing = false});

  @override
  State<PersonalInfoPage> createState() => _PersonalInfoPageState();
}

class _PersonalInfoPageState extends State<PersonalInfoPage> {
  // Controllers - Legacy (for backward compatibility)
  UserController userController = Get.find<UserController>();
  LocationController setLocation = Get.find<LocationController>();

  // New split controllers
  late UserDataController userDataController;
  late UserFormController userFormController;
  late UserSessionController userSessionController;

  // Country selection variables
  late List<Country> _countryList;
  late Country _selectedCountry;
  late List<Country> filteredCountries;

  // Form keys for validation
  final formkey = GlobalKey<FormState>();
  final formkeyLocation = GlobalKey<FormState>();
  TextEditingController codeController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Initialize new split controllers
    userDataController = Get.find<UserDataController>();
    userFormController = Get.find<UserFormController>();
    userSessionController = Get.find<UserSessionController>();

    _countryList = countries;
    filteredCountries = _countryList;
    _selectedCountry = _countryList.firstWhere(
        (item) => item.code == (Constants.defaultCountryData.code),
        orElse: () => _countryList.first);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.whitePure,
      resizeToAvoidBottomInset: widget.isEditing ? false : null,
      // APPBAR ----------
      appBar: AppbarCommon(
        title: widget.isEditing ? 'My profile'.tr : 'Personal information'.tr,
        appbar: AppBar(),
      ),
      // APPBAR ##########

      // BODY ----------
      body: Obx(() => CustomLoadingOverlay(
            opacity: 0.2,
            isLoading: userController.isLoading.value,
            progressIndicator: const CustomProgressIndicator(),
            child: widget.isEditing
                ? _buildEditProfileBody()
                : _buildPersonalInfoBody(),
          )),
      // BODY ##########
    );
  }

  /// Builds the edit profile form when user is editing their profile
  Widget _buildEditProfileBody() {
    return SafeArea(
      child: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  const SizedBox(height: 29.65),
                  heading("Mobile".tr),
                  const SizedBox(height: 8),
                  Form(
                    key: formkey,
                    child: Padding(
                      padding: const EdgeInsets.only(left: 18, right: 18),
                      child: Container(
                        decoration: BoxDecoration(
                          color: AppColors.greyLight,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: CustomTextfield(
                          isSettings: true,
                          controller: userController.emailController,
                          readOnly: true,
                          isPass: false,
                          onChanged: (val) {
                            if (formkey.currentState!.validate()) {
                              userController.setPersonalInfoData();
                              userController.getUserData();
                            }
                            return null;
                          },
                          validation: VALIDATIONS.validateRequired,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  heading("Location".tr),
                  const SizedBox(height: 8),
                  Form(
                    key: formkeyLocation,
                    child: Container(
                      padding: const EdgeInsets.only(left: 18, right: 18),
                      child: CustomTextfield(
                        isSettings: true,
                        controller: userController.locationController,
                        isPass: false,
                        validation: VALIDATIONS.validateRequired,
                        icon: IconButton(
                          onPressed: () async {
                            await setLocation.getCurrentLocation(
                                isFromEdit: true);

                            if (setLocation.lat.value != 0.0) {
                              userController.locationController.text =
                                  "${setLocation.district.value}, ${setLocation.city.value}, ${setLocation.country.value}";

                              await setLocation.setLocationData(
                                  lat: setLocation.lat.value,
                                  lng: setLocation.lng.value,
                                  city: setLocation.city.value,
                                  country: setLocation.country.value,
                                  district: setLocation.district.value,
                                  isFromEdit: true);

                              ToastCustom.successToast(
                                  "Location has been updated".tr);
                              userController.getUserData();
                            } else {
                              var result = await Get.to(
                                  const LocationPage(isFromEdit: true));

                              if (result != null &&
                                  setLocation.lat.value != 0.0) {
                                userController.locationController.text =
                                    "${setLocation.district.value}, ${setLocation.city.value}, ${setLocation.country.value}";

                                await setLocation.setLocationData(
                                    lat: setLocation.lat.value,
                                    lng: setLocation.lng.value,
                                    city: setLocation.city.value,
                                    country: setLocation.country.value,
                                    district: setLocation.district.value,
                                    isFromEdit: true);

                                ToastCustom.successToast(
                                    "Location has been updated".tr);
                                userController.getUserData();
                              }
                            }
                          },
                          icon: SvgPicture.asset(AppIcons.locationSyncIcon,
                              height: 32, width: 32),
                        ),
                        hint: 'Saudi Arabia, Riyadh',
                        readOnly: true,
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),
                  Container(
                    margin: const EdgeInsets.only(left: 18, right: 18),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            heading("Nationality".tr, isNoMargin: true),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.greyLight,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              margin: const EdgeInsets.only(left: 13.59),
                              width: MediaQuery.of(context).size.width * 0.5,
                              child: CustomTextfield(
                                isSettings: true,
                                controller:
                                    userController.nationalityController,
                                isPass: false,
                                validation: VALIDATIONS.validateRequired,
                                hint: 'Select'.tr,
                                readOnly: true,
                              ),
                            ),
                          ],
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            heading('Year Birthday'.tr, isNoMargin: true),
                            const SizedBox(height: 8),
                            Container(
                              decoration: BoxDecoration(
                                color: AppColors.greyLight,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              width: MediaQuery.of(context).size.width * 0.35,
                              child: CustomTextfield(
                                isSettings: true,
                                isPass: false,
                                readOnly: true,
                                inputFormatters: [
                                  LengthLimitingTextInputFormatter(
                                      Constants.yearLength),
                                  FilteringTextInputFormatter.allow(
                                      RegExp(r'^[0-9]+$'))
                                ],
                                keyboardType: TextInputType.number,
                                controller: userController.yearController,
                                validation: VALIDATIONS.validateYear,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        top: 23.1,
                        right: 20,
                        left: MediaQuery.of(context).size.width * 0.35),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: ListTile(
                            title: Text(
                              "Male".tr,
                              style: stylePrimaryLarge.copyWith(
                                  color: userController.gender == 'male'
                                      ? AppColors.primaryPurple
                                      : AppColors.greyDark.withOpacity(0.5)),
                            ),
                            contentPadding: EdgeInsets.zero,
                            visualDensity: const VisualDensity(
                                horizontal: VisualDensity.minimumDensity,
                                vertical: VisualDensity.minimumDensity),
                            leading: Transform.scale(
                              scale: 1.4,
                              child: Radio(
                                fillColor:
                                    WidgetStateProperty.resolveWith<Color>(
                                        (Set<WidgetState> states) {
                                  return userController
                                          .showGenderValidation.value
                                      ? AppColors.red
                                      : userController.gender == 'male'
                                          ? AppColors.primaryPurple
                                          : AppColors.greyDarkest
                                              .withOpacity(0.5);
                                }),
                                value: "male",
                                groupValue: userController.gender,
                                onChanged:
                                    null, // Disabled - gender cannot be changed
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: ListTile(
                            title: Text("Female".tr,
                                style: stylePrimaryLarge.copyWith(
                                    color: userController.gender == 'female'
                                        ? AppColors.primaryPurple
                                        : AppColors.greyDark.withOpacity(0.5))),
                            contentPadding: EdgeInsets.zero,
                            visualDensity: const VisualDensity(
                                horizontal: VisualDensity.minimumDensity,
                                vertical: VisualDensity.minimumDensity),
                            leading: Transform.scale(
                              scale: 1.4,
                              child: Radio(
                                fillColor:
                                    WidgetStateProperty.resolveWith<Color>(
                                        (Set<WidgetState> states) {
                                  return userController
                                          .showGenderValidation.value
                                      ? AppColors.red
                                      : userController.gender == 'female'
                                          ? AppColors.primaryPurple
                                          : AppColors.greyDarkest
                                              .withOpacity(0.5);
                                }),
                                value: "female",
                                groupValue: userController.gender,
                                onChanged:
                                    null, // Disabled - gender cannot be changed
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Column(
            children: [
              InkWell(
                onTap: () => showDeleteDialog(
                    context, "Delete Account".tr, "sure_del_account".tr),
                child: Padding(
                  padding: const EdgeInsets.only(top: 15, bottom: 16),
                  child: Text('Delete Account'.tr, style: styleRedLarge),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Builds the personal information form for new users
  Widget _buildPersonalInfoBody() {
    return Column(
      children: [
        Expanded(
          child: SingleChildScrollView(
            child: Form(
              key: formkey,
              child: Column(
                children: [
                  const SizedBox(height: 47.65),
                  Container(
                    padding: const EdgeInsets.only(left: 18, right: 18),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          height: MediaQuery.of(context).size.height * 0.13,
                          padding: const EdgeInsets.only(left: 13.59),
                          width: MediaQuery.of(context).size.width * 0.52,
                          child: CustomTextfield(
                            controller: userController.nationalityController,
                            isPass: false,
                            validation: VALIDATIONS.validateRequired,
                            label: 'Nationality'.tr,
                            icon: IconButton(
                                onPressed: _changeCountry,
                                icon: const Icon(Icons.arrow_drop_down)),
                            hint: 'Select'.tr,
                            readOnly: true,
                            onTap: () {
                              _changeCountry();
                              return null;
                            },
                          ),
                        ),
                        SizedBox(
                          height: MediaQuery.of(context).size.height * 0.13,
                          width: MediaQuery.of(context).size.width * 0.37,
                          child: CustomTextfield(
                            isPass: false,
                            inputFormatters: [
                              LengthLimitingTextInputFormatter(
                                  Constants.yearLength),
                              FilteringTextInputFormatter.allow(
                                  RegExp(r'^[0-9]+$'))
                            ],
                            keyboardType: TextInputType.number,
                            controller: userController.yearController,
                            validation: VALIDATIONS.validateYear,
                            label: 'Year Birthday'.tr,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(
                        right: 20,
                        left: MediaQuery.of(context).size.width * 0.35),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: ListTile(
                            title: Text(
                              "Male".tr,
                              style: stylePrimaryLarge.copyWith(
                                  color:
                                      userController.showGenderValidation.value
                                          ? AppColors.red
                                          : AppColors.primaryPurple),
                            ),
                            contentPadding: EdgeInsets.zero,
                            visualDensity: const VisualDensity(
                                horizontal: VisualDensity.minimumDensity,
                                vertical: VisualDensity.minimumDensity),
                            leading: Transform.scale(
                              scale: 1.4,
                              child: Radio(
                                fillColor:
                                    WidgetStateProperty.resolveWith<Color>(
                                        (Set<WidgetState> states) {
                                  return userController
                                          .showGenderValidation.value
                                      ? AppColors.red
                                      : userController.gender == 'male'
                                          ? AppColors.primaryPurple
                                          : AppColors.greyDarkest;
                                }),
                                value: "male",
                                groupValue: userController.gender,
                                onChanged: (value) {
                                  setState(() {
                                    userController.gender = value.toString();
                                    userController.showGenderValidation.value =
                                        false;
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                        Expanded(
                          child: ListTile(
                            title: Text("Female".tr,
                                style: stylePrimaryLarge.copyWith(
                                    color: userController
                                            .showGenderValidation.value
                                        ? AppColors.red
                                        : AppColors.primaryPurple)),
                            contentPadding: EdgeInsets.zero,
                            visualDensity: const VisualDensity(
                                horizontal: VisualDensity.minimumDensity,
                                vertical: VisualDensity.minimumDensity),
                            leading: Transform.scale(
                              scale: 1.4,
                              child: Radio(
                                fillColor:
                                    WidgetStateProperty.resolveWith<Color>(
                                        (Set<WidgetState> states) {
                                  return userController
                                          .showGenderValidation.value
                                      ? AppColors.red
                                      : userController.gender == 'female'
                                          ? AppColors.primaryPurple
                                          : AppColors.greyDarkest;
                                }),
                                value: "female",
                                groupValue: userController.gender,
                                onChanged: (value) {
                                  setState(() {
                                    userController.gender = value.toString();
                                    userController.showGenderValidation.value =
                                        false;
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  !userController.showGenderValidation.value
                      ? Container()
                      : Container(
                          margin: const EdgeInsets.only(
                              left: 29, right: 29, top: 23),
                          child: Row(
                            children: [
                              SvgPicture.asset(AppIcons.genderValidationIcon,
                                  width: 14, height: 14),
                              const SizedBox(width: 14),
                              Text('must_selected'.tr, style: styleRedLarge),
                            ],
                          ),
                        )
                ],
              ),
            ),
          ),
        ),
        Container(
          color: AppColors.greyLight,
          height: 1,
          width: double.infinity,
        ),
        Container(
          margin: const EdgeInsets.only(
              left: 27.5, right: 27.5, top: 25, bottom: 25),
          child: CustomButton(
            text: 'next'.tr,
            height: 46,
            onPressed: () {
              if (kDebugMode) {
                print("Selected gender: ${userController.gender ?? 'NULL'}");
              }

              if (formkey.currentState!.validate() &&
                  userController.gender != null &&
                  userController.gender!.isNotEmpty) {
                confirmationDialog();
              } else {
                if (userController.gender == null ||
                    userController.gender!.isEmpty) {
                  userController.showGenderValidation.value = true;
                  if (kDebugMode) {
                    print(
                        "Gender validation triggered: ${userController.showGenderValidation.value}");
                  }
                }
                if (userController.nationalityController.text.isEmpty ||
                    userController.yearController.text.isEmpty) {
                  userController.showValidationError.value = true;
                }
              }
            },
          ),
        ),
      ],
    );
  }

  Widget heading(String text, {bool? isNoMargin}) {
    return Container(
      alignment: Alignment.centerRight,
      padding: EdgeInsets.only(
          left: isNoMargin ?? false ? 0 : 18,
          right: isNoMargin ?? false ? 0 : 18),
      child: Text(
        text,
        textAlign: TextAlign.start,
        style: stylePrimaryLarge,
      ),
    );
  }

  /// Opens a dialog to select nationality from a list of countries
  Future<void> _changeCountry() async {
    filteredCountries = _countryList;
    await showDialog(
      context: context,
      useRootNavigator: false,
      builder: (context) => StatefulBuilder(
        builder: (ctx, setState) => NationalityPickerDialogCustom(
          style: NationalityPickerDialogStyleCustom(
              backgroundColor: AppColors.whitePure),
          filteredCountries: filteredCountries,
          searchText: '',
          countryList: _countryList,
          selectedCountry: _selectedCountry,
          onCountryChanged: (Country country) {
            _selectedCountry = country;
            userController.nationalityController.text =
                _selectedCountry.localizedName('ar');
          },
          languageCode: 'ar',
        ),
      ),
    );
    if (mounted) setState(() {});
  }

  /// Shows a confirmation dialog before saving profile information
  /// Warns user that some details cannot be changed later
  confirmationDialog() {
    SimpleDialog confirmDialog = SimpleDialog(
      contentPadding:
          const EdgeInsets.only(top: 24, bottom: 24, left: 16, right: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
      backgroundColor: AppColors.whitePure,
      insetPadding: const EdgeInsets.all(12.5),
      children: <Widget>[
        Material(
          type: MaterialType.transparency,
          color: AppColors.whitePure,
          child: Column(
            children: <Widget>[
              SvgPicture.asset(
                AppIcons.confirmIcon,
                width: 46,
                height: 46,
              ),
              const SizedBox(
                height: 18,
              ),
              Text(
                "Confirm Profile Details".tr,
                style: styleBlackLarge,
              ),
              // const SizedBox(
              //   height: 12,
              // ),
              // Text(
              //   "edit_warning".tr,
              //   textAlign: TextAlign.center,
              //   style: styleBlackLarge,
              // ),
              const SizedBox(
                height: 26,
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Expanded(
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width * 0.35,
                      child: CustomButton(
                        text: 'edit_now'.tr,
                        height: 44,
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ),
                  ),
                  const SizedBox(
                    width: 23.79,
                  ),
                  Expanded(
                    child: SizedBox(
                      width: MediaQuery.of(context).size.width * 0.35,
                      child: CustomButton(
                        bgColor: AppColors.greyLightest.withOpacity(0.8),
                        textColor: AppColors.blackDarkGray,
                        text: 'my_info_correct'.tr,
                        height: 44,
                        onPressed: () {
                          Get.back();
                          userController.setPersonalInfoData();
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );

    showDialog(context: context, builder: (context) => confirmDialog);
  }

  /// Shows a dialog to confirm account deletion
  /// Requires additional confirmation for safety
  void showDeleteDialog(BuildContext context, String heading, String text) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: AppColors.blackPure.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 0),
      pageBuilder: (_, __, ___) {
        return Material(
          type: MaterialType.transparency,
          child: Center(
            child: Container(
              width: double.infinity,
              margin: const EdgeInsets.symmetric(horizontal: 24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24),
                color: AppColors.whitePure,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 26.56,
                  ),
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                        color: AppColors.red.withOpacity(0.1),
                        shape: BoxShape.circle),
                    child: Container(
                      alignment: Alignment.center,
                      child: SvgPicture.asset(
                        AppIcons.deleteAccountIcon,
                        width: 28,
                        height: 28,
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 24,
                  ),
                  Text(
                    heading,
                    style: styleBlackLarge,
                  ),
                  const SizedBox(height: 21.88),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      text,
                      textAlign: TextAlign.center,
                      style: styleBlackLarge,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.only(left: 16.6, right: 16.6),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        InkWell(
                          onTap: () {
                            ///close dialog
                            Get.back();
                          },
                          child: Container(
                            height: 52,
                            width: MediaQuery.of(context).size.width * 0.35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.greyDark.withOpacity(0.2),
                                    spreadRadius: 0.1,
                                    blurRadius: 0.1,
                                    offset: const Offset(
                                        0, 0.5), // changes position of shadow
                                  ),
                                ],
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'Cancel'.tr,
                              style: styleBlackLarge,
                            ),
                          ),
                        ),
                        InkWell(
                          onTap: () async {
                            Get.back();
                            String randomNumber = Random()
                                .nextInt(9999)
                                .toString()
                                .padLeft(4, '0');
                            if (kDebugMode) {
                              print("object:$randomNumber");
                            }
                            showDeleteConfirmDialog(
                                context,
                                "Confirm Account Deletion".tr,
                                "confirm_del_account".tr,
                                randomNumber);
                          },
                          child: Container(
                            height: 52,
                            width: MediaQuery.of(context).size.width * 0.35,
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                                color: AppColors.red,
                                borderRadius: BorderRadius.circular(10)),
                            child: Text(
                              'Delete'.tr,
                              style: styleWhiteLarge,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        return FadeTransition(
          opacity: anim,
          child: child,
        );
      },
    );
  }

  /// Final confirmation dialog for account deletion
  /// Requires user to enter a random code for verification
  void showDeleteConfirmDialog(
      BuildContext context, String heading, String text, String randomNumber) {
    showGeneralDialog(
      context: context,
      barrierLabel: "Barrier",
      barrierDismissible: true,
      barrierColor: AppColors.blackPure.withOpacity(0.5),
      transitionDuration: const Duration(milliseconds: 0),
      pageBuilder: (_, __, ___) {
        return GestureDetector(
          onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
          child: Material(
            type: MaterialType.transparency,
            child: Center(
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  color: AppColors.whitePure,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const SizedBox(
                      height: 26.56,
                    ),
                    Text(
                      heading,
                      style: styleBlackLarge,
                    ),
                    const SizedBox(height: 21.88),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        text,
                        textAlign: TextAlign.center,
                        style: styleBlackLarge,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Container(
                      margin: const EdgeInsets.only(left: 30, right: 30),
                      alignment: Alignment.center,
                      child: Row(
                        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.only(
                                top: 6.37,
                                left: 15.91,
                                right: 15.91,
                                bottom: 6.37),
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                borderRadius: BorderRadius.circular(4.774)),
                            child: Text(
                              randomNumber.substring(3, 4),
                              textAlign: TextAlign.center,
                              style: styleBlackLarge,
                            ),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Container(
                            padding: const EdgeInsets.only(
                                top: 6.37,
                                left: 15.91,
                                right: 15.91,
                                bottom: 6.37),
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                borderRadius: BorderRadius.circular(4.774)),
                            child: Text(
                              randomNumber.substring(2, 3),
                              textAlign: TextAlign.center,
                              style: styleBlackLarge,
                            ),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Container(
                            padding: const EdgeInsets.only(
                                top: 6.37,
                                left: 15.91,
                                right: 15.91,
                                bottom: 6.37),
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                borderRadius: BorderRadius.circular(4.774)),
                            child: Text(
                              randomNumber.substring(1, 2),
                              textAlign: TextAlign.center,
                              style: styleBlackLarge,
                            ),
                          ),
                          const SizedBox(
                            width: 12,
                          ),
                          Container(
                            padding: const EdgeInsets.only(
                                top: 6.37,
                                left: 15.91,
                                right: 15.91,
                                bottom: 6.37),
                            decoration: BoxDecoration(
                                color: AppColors.greyLightest,
                                borderRadius: BorderRadius.circular(4.774)),
                            child: Text(
                              randomNumber.substring(0, 1),
                              textAlign: TextAlign.center,
                              style: styleBlackLarge,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                    Container(
                        margin: const EdgeInsets.symmetric(horizontal: 34),
                        child: CustomTextfield(
                          keyboardType: TextInputType.phone,
                          validation: VALIDATIONS.validateRequired,
                          isPass: false,
                          hint: "Enter the code here".tr,
                          controller: codeController,
                        )),
                    const SizedBox(height: 24),
                    Container(
                      padding: const EdgeInsets.only(left: 16.6, right: 16.6),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InkWell(
                            onTap: () {
                              ///close dialog
                              codeController.clear();
                              Get.back();
                            },
                            child: Container(
                              height: 52,
                              width: MediaQuery.of(context).size.width * 0.35,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color:
                                      AppColors.greyLightest.withOpacity(0.8),
                                  boxShadow: [
                                    BoxShadow(
                                      color:
                                          AppColors.greyDark.withOpacity(0.2),
                                      spreadRadius: 0.1,
                                      blurRadius: 0.1,
                                      offset: const Offset(
                                          0, 0.5), // changes position of shadow
                                    ),
                                  ],
                                  borderRadius: BorderRadius.circular(10)),
                              child: Text(
                                'Cancel'.tr,
                                style: styleBlackLarge,
                              ),
                            ),
                          ),
                          InkWell(
                            onTap: () async {
                              if (codeController.text == randomNumber) {
                                userController.deleteAccount();
                                codeController.clear();
                                Get.back();
                              }
                            },
                            child: Container(
                              height: 52,
                              width: MediaQuery.of(context).size.width * 0.35,
                              alignment: Alignment.center,
                              decoration: BoxDecoration(
                                  color: AppColors.red,
                                  borderRadius: BorderRadius.circular(10)),
                              child: Text(
                                'Confirm'.tr,
                                style: styleWhiteLarge,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        );
      },
      transitionBuilder: (_, anim, __, child) {
        return FadeTransition(
          opacity: anim,
          child: child,
        );
      },
    );
  }
}
