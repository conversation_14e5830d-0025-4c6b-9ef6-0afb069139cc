/// Nationality Picker Dialog
///
/// A customizable dialog for selecting a nationality from a list of countries
/// Supports search functionality and localized country names
library nationality_picker_dialog_custom;

import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:towasl/core/helpers/country_helper_custom.dart';
import 'package:towasl/core/helpers/custom_countries.dart';

/// Style configuration for the nationality picker dialog
///
/// Allows customization of colors, text styles, padding, and other visual aspects
class NationalityPickerDialogStyleCustom {
  /// Background color of the dialog
  final Color? backgroundColor;

  /// Text style for country codes
  final TextStyle? countryCodeStyle;

  /// Text style for country names
  final TextStyle? countryNameStyle;

  /// Divider widget between list items
  final Widget? listTileDivider;

  /// Padding for list tiles
  final EdgeInsets? listTilePadding;

  /// Padding for the entire dialog
  final EdgeInsets? padding;

  /// Cursor color for the search field
  final Color? searchFieldCursorColor;

  /// Input decoration for the search field
  final InputDecoration? searchFieldInputDecoration;

  /// Padding for the search field
  final EdgeInsets? searchFieldPadding;

  /// Width of the dialog
  final double? width;

  /// Creates a NationalityPickerDialogStyleCustom
  ///
  /// All parameters are optional and will use default values if not provided
  NationalityPickerDialogStyleCustom({
    this.backgroundColor,
    this.countryCodeStyle,
    this.countryNameStyle,
    this.listTileDivider,
    this.listTilePadding,
    this.padding,
    this.searchFieldCursorColor,
    this.searchFieldInputDecoration,
    this.searchFieldPadding,
    this.width,
  });
}

/// Dialog for selecting a nationality from a list of countries
///
/// Displays a searchable list of countries with flags and localized names
/// Used in the personal information page for nationality selection
class NationalityPickerDialogCustom extends StatefulWidget {
  /// Complete list of available countries
  final List<Country> countryList;

  /// Currently selected country
  final Country selectedCountry;

  /// Callback when a country is selected
  final ValueChanged<Country> onCountryChanged;

  /// Placeholder text for the search field
  final String searchText;

  /// Pre-filtered list of countries to display
  final List<Country> filteredCountries;

  /// Style configuration for the dialog
  final NationalityPickerDialogStyleCustom? style;

  /// Language code for displaying localized country names
  final String languageCode;

  /// Creates a NationalityPickerDialogCustom
  ///
  /// @param searchText Placeholder text for the search field
  /// @param languageCode Language code for localized country names
  /// @param countryList Complete list of available countries
  /// @param onCountryChanged Callback when a country is selected
  /// @param selectedCountry Currently selected country
  /// @param filteredCountries Pre-filtered list of countries to display
  /// @param style Optional style configuration
  const NationalityPickerDialogCustom({
    super.key,
    required this.searchText,
    required this.languageCode,
    required this.countryList,
    required this.onCountryChanged,
    required this.selectedCountry,
    required this.filteredCountries,
    this.style,
  });

  @override
  NationalityPickerDialogCustomState createState() =>
      NationalityPickerDialogCustomState();
}

/// State for the NationalityPickerDialogCustom
class NationalityPickerDialogCustomState
    extends State<NationalityPickerDialogCustom> {
  /// Current filtered list of countries based on search
  late List<Country> _filteredCountries;

  /// Currently selected country
  late Country _selectedCountry;

  @override
  void initState() {
    _selectedCountry = widget.selectedCountry;
    _filteredCountries = widget.filteredCountries.toList()
        /*..sort(
            (a, b) => a.localizedName(widget.languageCode).compareTo(b.localizedName(widget.languageCode)),
      )*/
        ;

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final mediaWidth = MediaQuery.of(context).size.width;
    final width = widget.style?.width ?? mediaWidth;
    const defaultHorizontalPadding = 40.0;
    const defaultVerticalPadding = 24.0;
    return Dialog(
      insetPadding: EdgeInsets.symmetric(
          vertical: defaultVerticalPadding,
          horizontal: mediaWidth > (width + defaultHorizontalPadding * 2)
              ? (mediaWidth - width) / 2
              : defaultHorizontalPadding),
      backgroundColor: widget.style?.backgroundColor,
      child: Container(
        padding: widget.style?.padding ?? const EdgeInsets.all(10),
        child: Column(
          children: <Widget>[
            Padding(
              padding:
                  widget.style?.searchFieldPadding ?? const EdgeInsets.all(0),
              child: TextField(
                cursorColor: widget.style?.searchFieldCursorColor,
                decoration: widget.style?.searchFieldInputDecoration ??
                    InputDecoration(
                      suffixIcon: const Icon(Icons.search),
                      labelText: widget.searchText,
                    ),
                onChanged: (value) {
                  _filteredCountries = widget.countryList.stringSearch(value)
                    ..sort(
                      (a, b) => a
                          .localizedName(widget.languageCode)
                          .compareTo(b.localizedName(widget.languageCode)),
                    );
                  if (mounted) setState(() {});
                },
              ),
            ),
            const SizedBox(height: 20),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: _filteredCountries.length,
                itemBuilder: (ctx, index) => Column(
                  children: <Widget>[
                    ListTile(
                      leading: kIsWeb
                          ? Image.asset(
                              'assets/flags/${_filteredCountries[index].code.toLowerCase()}.png',
                              package: 'intl_phone_field',
                              width: 32,
                            )
                          : Text(
                              _filteredCountries[index].flag,
                              style: const TextStyle(fontSize: 18),
                            ),
                      contentPadding: widget.style?.listTilePadding,
                      title: Text(
                        _filteredCountries[index]
                            .localizedName(widget.languageCode),
                        style: widget.style?.countryNameStyle ??
                            const TextStyle(fontWeight: FontWeight.w700),
                      ),
                      /*trailing: Text(
                        '+${_filteredCountries[index].dialCode}',
                        style: widget.style?.countryCodeStyle ?? const TextStyle(fontWeight: FontWeight.w700),
                      ),*/
                      onTap: () {
                        _selectedCountry = _filteredCountries[index];
                        widget.onCountryChanged(_selectedCountry);
                        Navigator.of(context).pop();
                      },
                    ),
                    widget.style?.listTileDivider ??
                        const Divider(thickness: 1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
