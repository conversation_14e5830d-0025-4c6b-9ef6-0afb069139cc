/// Mobile OTP Verification Page
///
/// Allows new users to enter a verification code sent to their mobile number during signup
/// Provides options to resend the code and verify the entered code
library mobile_otp_page;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:towasl/controllers/auth_controllers/otp_verification_controller.dart';
import 'package:towasl/core/theme/app_color.dart';
import 'package:towasl/core/theme/app_text_styles.dart';
import 'package:towasl/views/widgets/custom_button.dart';
import 'package:towasl/views/widgets/custom_loading_overlay.dart';
import 'package:towasl/views/widgets/progress_indicator.dart';
import 'package:towasl/views/widgets/toasts_custom.dart';

/// Mobile OTP Verification Page
class MobileOtpPage extends StatefulWidget {
  /// Creates a MobileOtpPage
  const MobileOtpPage({super.key});

  @override
  State<MobileOtpPage> createState() => _MobileOtpPageState();
}

class _MobileOtpPageState extends State<MobileOtpPage> {
  /// Controller for OTP verification
  final OtpVerificationController otpController = Get.find();

  /// Controllers for the 4 OTP input fields
  final TextEditingController _fieldOne = TextEditingController();
  final TextEditingController _fieldTwo = TextEditingController();
  final TextEditingController _fieldThree = TextEditingController();
  final TextEditingController _fieldFour = TextEditingController();

  /// Focus nodes for the 4 OTP input fields
  final FocusNode _focusOne = FocusNode();
  final FocusNode _focusTwo = FocusNode();
  final FocusNode _focusThree = FocusNode();
  final FocusNode _focusFour = FocusNode();

  @override
  void initState() {
    super.initState();
    // Set up focus node listeners to automatically move to the next field
    _focusOne.addListener(() {
      if (_fieldOne.text.isNotEmpty && _focusOne.hasFocus) {
        FocusScope.of(context).requestFocus(_focusTwo);
      }
    });

    _focusTwo.addListener(() {
      if (_fieldTwo.text.isNotEmpty && _focusTwo.hasFocus) {
        FocusScope.of(context).requestFocus(_focusThree);
      }
    });

    _focusThree.addListener(() {
      if (_fieldThree.text.isNotEmpty && _focusThree.hasFocus) {
        FocusScope.of(context).requestFocus(_focusFour);
      }
    });

    // Set focus on the first input field when the page opens
    // Using a small delay to ensure the widget is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(_focusOne);
    });
  }

  @override
  void dispose() {
    // Clean up controllers and focus nodes
    _fieldOne.dispose();
    _fieldTwo.dispose();
    _fieldThree.dispose();
    _fieldFour.dispose();

    _focusOne.dispose();
    _focusTwo.dispose();
    _focusThree.dispose();
    _focusFour.dispose();

    super.dispose();
  }

  /// Get the combined OTP from all fields
  String get _otp =>
      '${_fieldOne.text}${_fieldTwo.text}${_fieldThree.text}${_fieldFour.text}';

  /// Verify the entered OTP
  void _verifyOtp() {
    // Unfocus any active field
    FocusScope.of(context).unfocus();

    // Check if all fields are filled
    if (_otp.length != 4) {
      // Show warning toast if OTP is not complete
      ToastCustom.warningToastCustom(
          'please_enter_complete_otp'.tr, context, true);
      return;
    }

    // Verify the OTP
    otpController.verifyOtp(_otp);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Unfocus when tapping outside input fields
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: AppColors.whitePure,
        body: Obx(
          () => CustomLoadingOverlay(
            opacity: 0.2,
            isLoading: otpController.isLoading.value ||
                otpController.isVerifying.value,
            progressIndicator: const CustomProgressIndicator(),
            child: SafeArea(
              child: Center(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(height: 40),

                      // VERIFICATION TITLE
                      Text(
                        'verification_code'.tr,
                        style: styleBlackLarge.copyWith(
                          fontSize: 18,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 20),

                      // OTP SENT MESSAGE
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40),
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: styleBlackNormal,
                            children: [
                              TextSpan(
                                text: '${'enter_otp_sent'.tr} ',
                              ),
                              TextSpan(
                                text: otpController.mobileNumber,
                                style: styleBlackNormal.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 5),

                      // SENDER NAME
                      Text(
                        'ashkal_verification'.tr,
                        style: styleBlackNormal,
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 30),

                      // OTP INPUT FIELDS
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // FIELD 4 (rightmost in RTL)
                            _buildOtpField(
                              controller: _fieldFour,
                              focusNode: _focusFour,
                              previousFocus: _focusThree,
                              isLast: true,
                            ),

                            // FIELD 3
                            _buildOtpField(
                              controller: _fieldThree,
                              focusNode: _focusThree,
                              previousFocus: _focusTwo,
                              nextFocus: _focusFour,
                            ),

                            // FIELD 2
                            _buildOtpField(
                              controller: _fieldTwo,
                              focusNode: _focusTwo,
                              previousFocus: _focusOne,
                              nextFocus: _focusThree,
                            ),

                            // FIELD 1 (leftmost in RTL)
                            _buildOtpField(
                              controller: _fieldOne,
                              focusNode: _focusOne,
                              nextFocus: _focusTwo,
                              isFirst: true,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 30),

                      // TIMER
                      Obx(
                        () => Text(
                          '${otpController.formattedTime} ${'sec'.tr}',
                          style: styleBlackNormal.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 10),

                      // RESEND OTP BUTTON
                      GestureDetector(
                        onTap: () => otpController.resendOtp(),
                        child: Text(
                          'to_re_send_otp_code'.tr,
                          style: styleFushiNormal.copyWith(
                            decoration: TextDecoration.underline,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      const SizedBox(height: 30),

                      // VERIFY BUTTON
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 40),
                        child: CustomButton(
                          text: 'go'.tr,
                          onPressed: _verifyOtp,
                          height: 50,
                          bgColor: AppColors.primaryPurple,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// Build an individual OTP input field
  Widget _buildOtpField({
    required TextEditingController controller,
    required FocusNode focusNode,
    FocusNode? nextFocus,
    FocusNode? previousFocus,
    bool isLast = false,
    bool isFirst = false,
  }) {
    return SizedBox(
      width: 65,
      height: 65,
      child: TextFormField(
        controller: controller,
        focusNode: focusNode,
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        style: styleBlackLarge.copyWith(
          fontSize: 20,
          fontWeight: FontWeight.w600,
        ),
        maxLength: 1,
        decoration: InputDecoration(
          counterText: '',
          contentPadding: EdgeInsets.zero,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.greyLight, width: 1),
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: AppColors.greyLight, width: 1),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide:
                const BorderSide(color: AppColors.primaryPurple, width: 2),
          ),
          filled: true,
          fillColor: AppColors.greyLightest,
        ),
        inputFormatters: [
          FilteringTextInputFormatter.digitsOnly,
        ],
        onChanged: (value) {
          if (value.isNotEmpty && nextFocus != null) {
            FocusScope.of(context).requestFocus(nextFocus);
          } else if (value.isEmpty && previousFocus != null) {
            FocusScope.of(context).requestFocus(previousFocus);
          }

          // Auto-verify when all fields are filled
          if (isLast && value.isNotEmpty && _otp.length == 4) {
            _verifyOtp();
          }
        },
      ),
    );
  }
}
