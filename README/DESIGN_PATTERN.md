# 🧱 Flutter Project Design Pattern Guide

This document defines the design pattern structure used in this project, following a layered and scalable architecture based on Clean Architecture principles.

---

## 📂 Layered Architecture Overview

The project is organized into three main layers:

1. **Data Layer** – Handles data fetching, storage, and modeling.
2. **Domain Layer** – Contains core business logic and rules.
3. **Presentation Layer** – Manages UI and state.

Additionally, we include shared modules (e.g., utilities, themes) under a **Shared/Core Layer**.

---

## 📁 Project Folder Structure

```plaintext
lib/
├── core/                     # Global config/constants
├── data/                     # Data sources and models
│   ├── models/               # Serializable data models (API/DB)
│   ├── repositories/         # Interfaces and implementations for data access
│   └── data_sources/         # API clients, local DB, etc.
├── domain/                   # Business logic and app rules
│   ├── entities/             # Core business entities
│   ├── usecases/             # Application-specific business use cases
│   └── services/             # Business logic helpers
├── presentation/             # UI and state management
│   ├── controllers/          # GetX/Bloc/etc. state management
│   ├── views/                # UI screens and pages
│   ├── widgets/              # Reusable UI components
│   └── bindings/             # GetX bindings (dependency injection)
├── shared/                   # Common/shared utilities
│   ├── utils/                # Helper functions and classes
│   ├── extensions/           # Dart extension methods
│   ├── themes/               # App-wide theming
│   └── l10n/                 # Localization files


## 🧠 Design Patterns and Definitions

| Pattern          | Layer        | Folder Path                     | Description                                                                   |
| ---------------- | ------------ | ------------------------------- | ----------------------------------------------------------------------------- |
| **Model**        | Data         | `lib/data/models/`              | Serializable classes used to fetch/submit data to APIs or DBs.                |
| **Repository**   | Data         | `lib/data/repositories/`        | Abstract interface to access data from sources. Used by UseCases/Controllers. |
| **Data Source**  | Data         | `lib/data/data_sources/`        | Actual implementation of APIs, shared preferences, local DBs, etc.            |
| **Entity**       | Domain       | `lib/domain/entities/`          | Core business objects, independent of frameworks.                             |
| **UseCase**      | Domain       | `lib/domain/usecases/`          | Single application action or business rule (e.g., Login, FetchPosts).         |
| **Service**      | Domain       | `lib/domain/services/`          | Reusable business logic helpers, used by use cases or repositories.           |
| **Controller**   | Presentation | `lib/presentation/controllers/` | UI state managers using GetX, Bloc, etc.                                      |
| **View**         | Presentation | `lib/presentation/views/`       | Flutter widgets/screens shown to users.                                       |
| **Widget**       | Presentation | `lib/presentation/widgets/`     | Reusable UI components like buttons, cards, etc.                              |
| **Binding**      | Presentation | `lib/presentation/bindings/`    | GetX-specific dependency injection bindings.                                  |
| **Helper/Util**  | Shared       | `lib/shared/utils/`             | Helper functions used app-wide (e.g., validators, formatters).                |
| **Extension**    | Shared       | `lib/shared/extensions/`        | Dart extensions for types like String, DateTime.                              |
| **Theme**        | Shared       | `lib/shared/themes/`            | Color and text theme definitions.                                             |
| **Localization** | Shared       | `lib/shared/l10n/`              | Localized string files for internationalization.                              |

---

## ✅ Notes

- **UseCases** should be thin and focused on one job (e.g., `LoginUser`, not `AuthManager`).
- **Repositories** hide all data-source details and return clean data (Entities).
- **Entities** are simple Dart classes without dependencies, suitable for testing and business logic.
- Use **Controllers** to connect UseCases and update UI state.

---

## 📌 Example Flow

1. **View** triggers → **Controller**
2. **Controller** calls → **UseCase**
3. **UseCase** uses → **Repository**
4. **Repository** uses → **Data Source**
5. **Data Source** fetches/saves → **Model**
6. **Model** is converted to → **Entity**
7. **Entity** is returned all the way back to the UI
