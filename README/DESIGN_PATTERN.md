# 🧱 Flutter Project Design Pattern Guide

This document defines the design pattern structure used in this project, following a layered and scalable architecture based on Clean Architecture principles.

---

## 📂 Layered Architecture Overview

The project is organized into three main layers:

1. **Data Layer** – Handles data fetching, storage, and modeling.
2. **Domain Layer** – Contains core business logic and rules.
3. **Presentation Layer** – Manages UI and state.

Additionally, we include shared modules (e.g., utilities, themes) under a **Shared/Core Layer**.

---

## 📁 Project Folder Structure

```plaintext
lib/
├── core/                     # Global config/constants
├── data/                     # Data sources and models
│   ├── models/               # Serializable data models (API/DB)
│   ├── repositories/         # Interfaces and implementations for data access
│   └── data_sources/         # API clients, local DB, etc.
├── domain/                   # Business logic and app rules
│   ├── entities/             # Core business entities
│   ├── usecases/             # Application-specific business use cases
│   └── services/             # Business logic helpers
├── presentation/             # UI and state management
│   ├── controllers/          # GetX/Bloc/etc. state management
│   ├── views/                # UI screens and pages
│   ├── widgets/              # Reusable UI components
│   └── bindings/             # GetX bindings (dependency injection)
├── shared/                   # Common/shared utilities
│   ├── utils/                # Helper functions and classes
│   ├── extensions/           # Dart extension methods
│   ├── themes/               # App-wide theming
│   └── l10n/                 # Localization files
