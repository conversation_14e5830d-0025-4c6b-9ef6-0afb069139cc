# Dependency Injection System

This directory contains the dependency injection system for the Towasl app. The system is built using GetX's dependency injection capabilities, but with a more structured and maintainable approach.

## Overview

The dependency injection system is organized as follows:

1. **Services**: Core services that provide functionality to the app
2. **Controllers**: Business logic components that use services
3. **Bindings**: Classes that register controllers and services with GetX

## Services

Services are registered as lazy singletons with the `fenix: true` option, which means they are created when first accessed and recreated if they are removed from memory.

### Available Services

- **StorageService**: Handles persistent storage using GetStorage
- **FirebaseService**: Provides access to Firebase services (Firestore, Messaging)
- **LocationService**: Manages location-related operations

## Controllers

Controllers are registered in different ways depending on their lifecycle:

- **AppController**: Registered as a permanent singleton
- **Core controllers** (UserController, LanguageController): Registered as lazy singletons with `fenix: true`
- **Feature controllers** (MobileLoginController, InterestsController, etc.): Registered as lazy singletons with `fenix: true`

## Bindings

Bindings are classes that register controllers and services with GetX. They are used to ensure that the required dependencies are available when a screen is loaded.

### Available Bindings

- **AppBinding**: Registers the AppController and core services
- **SignupLoginBinding**: Registers controllers for the signup/login flow
- **HomeBinding**: Registers controllers for the home screen
- **InterestsBinding**: Registers controllers for the interests screen
- **LocationBinding**: Registers controllers for the location screen

## Usage

### Accessing Services and Controllers

Services and controllers can be accessed using GetX's `Get.find()` method:

```dart
// Get a reference to a service
final firebaseService = Get.find<FirebaseService>();

// Get a reference to a controller
final appController = Get.find<AppController>();
```

### Adding a New Service

To add a new service:

1. Create a service interface and implementation in `lib/core/services/`
2. Register the service in `lib/core/di/dependency_injection.dart`

Example:

```dart
// Register the new service
Get.lazyPut<NewService>(() => NewServiceImpl(), fenix: true);
```

### Adding a New Controller

To add a new controller:

1. Create the controller in `lib/controllers/`
2. Update the controller to use dependency injection
3. Register the controller in `lib/core/di/dependency_injection.dart`

Example:

```dart
// Register the new controller
Get.lazyPut<NewController>(() => NewController(
  firebaseService: Get.find<FirebaseService>(),
  appController: Get.find<AppController>(),
));
```

### Adding a New Binding

To add a new binding:

1. Create a binding class in `lib/bindings/`
2. Register the required controllers in the binding

Example:

```dart
class NewBinding extends Bindings {
  @override
  void dependencies() {
    // Register controllers
    Get.lazyPut<NewController>(() => NewController(
      firebaseService: Get.find<FirebaseService>(),
      appController: Get.find<AppController>(),
    ));
  }
}
```

## Best Practices

1. **Use interfaces for services**: This makes it easier to mock services for testing
2. **Inject dependencies through constructors**: This makes dependencies explicit
3. **Use `Get.find()` only in bindings**: Avoid using `Get.find()` directly in controllers or views
4. **Check for required services in bindings**: Throw exceptions if required services are not registered
5. **Use `permanent: true` sparingly**: Only use for controllers that need to persist throughout the app's lifecycle
