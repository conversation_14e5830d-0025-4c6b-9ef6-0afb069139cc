# Towasl

A Flutter application for connecting people with similar interests.

## Getting Started

This project is a starting point for a Flutter application.

```bash
flutter clean
flutter pub get
cd ios
pod install
cd ..
open -a Simulator or emulator -avd <emulator_name>
flutter run or flutter run -d <device_id>
```

## Global State Management Refactoring

### Overview

This project has been refactored to use the AppController for global state management instead of global variables. The AppController provides a more maintainable and testable approach to managing application state.

### Changes Made

1. **Removed Global Variables**
   - The global variables in `lib/core/globals.dart` have been completely removed
   - All code now uses AppController for global state management

2. **Updated Controllers**
   - `SaveRegisterDataController` now uses AppController for user ID and other global state
   - `InterestsController` now uses AppController for user ID
   - `LocationController` now uses AppController for user ID
   - Other controllers have been updated to use AppController where needed

3. **Updated Views**
   - `HomePage` now uses AppController for displaying user ID
   - `SettingsPage` now uses AppController for logout functionality
   - `InterestsPage` now uses AppController for user ID validation

### How to Use AppController

The AppController is a singleton that can be accessed using GetX's dependency injection:

```dart
// Get a reference to the AppController
final AppController appController = Get.find<AppController>();

// Access user ID
String userId = appController.userId;

// Set user ID
appController.userId = "new_user_id";

// Access user model
UserModel userModel = appController.userModel;

// Set user model
appController.userModel = newUserModel;

// Clear user data (e.g., during logout)
appController.clearUserData();
```

### Benefits of Using AppController

1. **Centralized State Management**: All global state is managed in one place
2. **Type Safety**: Properties are strongly typed
3. **Reactive Updates**: Uses GetX's reactive variables for automatic UI updates
4. **Testability**: Easier to mock and test
5. **Maintainability**: Clearer code organization and dependencies

### Migration Complete

The migration from global variables to AppController is now complete. All code in the application now uses AppController for global state management, providing better maintainability and testability.

## Authentication System

### Overview

The app uses a custom mobile number authentication system with OTP verification. Firebase Auth has been removed in favor of this simpler approach.

### Authentication Flow

1. **Mobile Number Entry**: User enters their mobile number on the login screen
2. **OTP Verification**: A simulated OTP verification process is used (any 4-digit code is accepted)
3. **User Creation/Login**:
   - If the user exists in Firestore, they are logged in
   - If the user doesn't exist, a new user document is created in Firestore
4. **Session Management**:
   - User ID is stored in AppController and local storage
   - Firebase Messaging token is used for session tracking
   - User data is stored in Firestore

### Benefits of Custom Authentication

1. **Simplified Dependencies**: No need for Firebase Auth package
2. **Focused Functionality**: Only mobile authentication is needed
3. **Full Control**: Complete control over the authentication flow
4. **Easier Testing**: Simpler to test without Firebase Auth dependencies