# App State Management in Towasl

This document provides a comprehensive overview of how state is managed throughout the Towasl application.

## 1. GetX State Management

The app primarily uses GetX for state management with the following patterns:

### A. Controllers Hierarchy

1. **BaseController**
   - Provides common functionality for all controllers
   - Manages loading state with `RxBool isLoading = false.obs`
   - Methods: `startLoading()` and `stopLoading()`

2. **AppController** (extends BaseController)
   - Central controller for global application state
   - Registered as a permanent singleton
   - Manages:
     - User state: `RxString _userId` and `Rx<UserModel> _userModel`
     - API credentials: `RxString _msegatApikey`, `_msegatUserSender`, `_msegatUsername`
     - Authentication provider: `RxString _providerName`

3. **Feature-specific Controllers**
   - **UserController**: Manages user profile data and authentication
   - **InterestsController**: Manages user interests selection and storage
   - **LocationController**: Manages user location data and permissions
   - **MatchController**: Handles user matching functionality
   - **LanguageController**: Manages app localization settings
   - **SocialLoginController**: Handles social media authentication
   - **SaveRegisterDataController**: Manages user registration data

## 2. Reactive State Variables

### A. User State
- `_userId.obs`: User ID stored as reactive string
- `_userModel.obs`: Complete user model as reactive object
- `userModel.obs`: User model in UserController
- `isNotiEnabled.obs`: Notification preferences

### B. UI State
- `isLoading.obs`: Loading state in BaseController and feature controllers
- `showGenderValidation.obs`: Form validation state
- `showValidationError.obs`: Error display state
- `isOpenAppSetting.obs`: App settings state

### C. Location State
- `lat.obs`, `lng.obs`: Coordinates as reactive doubles
- `country.obs`, `city.obs`, `district.obs`: Location information

### D. Interest State
- `interestData.obs`: List of interest categories and selections

### E. Language State
- `currentLang.obs`: Current language code

## 3. Persistent Storage

The app uses GetStorage for persistent state storage:

### A. StorageService
- Interface defining storage operations
- Implemented by StorageServiceImpl using GetStorage
- Stores:
  - User login state: `loggedIn` flag
  - User ID: `userID`
  - Language preference: `langCode`
  - First-time app launch: `isFirstTime`
  - Terms acceptance: `termsAccepted`

### B. Storage Methods
- `setLoginData(String userID)`: Saves user login state
- `getLoggedInStatus()`: Retrieves login state
- `getUserIDValue()`: Gets stored user ID
- `setLanguage(String langCode)`: Saves language preference
- `getLanguage()`: Retrieves language setting
- `clearUserData()`: Clears user data while preserving terms acceptance
- `setFirstTime()`: Marks app as no longer first launch
- `getFirstTime()`: Checks if this is first app launch
- `setTermsAccepted(bool)`: Saves terms acceptance state
- `getTermsAccepted()`: Retrieves terms acceptance state

## 4. Dependency Injection

### A. Bindings
- **AppBinding**: Registers AppController and core services
- **SignupLoginBinding**: For signup/login flow
- **HomeBinding**: For home screen
- **InterestsBinding**: For interests screen
- **LocationBinding**: For location screen
- **PersonalInfoBinding**: For personal info screen

### B. Registration Types
- Permanent singletons: `Get.put(controller, permanent: true)`
- Persistent lazy singletons: `Get.lazyPut(controller, fenix: true)`
- Regular lazy singletons: `Get.lazyPut(controller)`

## 5. Remote State

### A. Firestore Data
- User profile information
- User interests
- User location
- Matching data

### B. Custom Authentication
- Mobile number authentication with OTP
- User session management via Firestore

## 6. Form State

### A. Text Controllers
- `nameController`
- `nationalityController`
- `yearController`
- `locationController`
- `emailController`

### B. Selection State
- `gender` variable
- Interest selections

## 7. Navigation State

- Managed through GetX navigation system
- Page transitions and history

## 8. Localization State

- Managed by LanguageController
- Persisted through StorageService
- Affects UI direction and text

## How to Access State

### Accessing AppController

```dart
// Get a reference to the AppController
final AppController appController = Get.find<AppController>();

// Access user ID
String userId = appController.userId;

// Set user ID
appController.userId = "new_user_id";

// Access user model
UserModel userModel = appController.userModel;

// Set user model
appController.userModel = newUserModel;

// Clear user data (e.g., during logout)
appController.clearUserData();
```

### Accessing Feature Controllers

```dart
// Get a reference to a feature controller
final UserController userController = Get.find<UserController>();

// Access user data
UserModel user = userController.userModel.value;

// Update user data
userController.getUserData();
```

### Using Reactive Variables in UI

```dart
// Using Obx to react to state changes
Obx(() => Text(userController.userModel.value.name ?? ""))

// Using GetX widget for more control
GetX<UserController>(
  builder: (controller) => Text(controller.userModel.value.name ?? ""),
)
```

## Best Practices

1. **Use AppController for global state**: Avoid global variables
2. **Inject dependencies through constructors**: Makes dependencies explicit
3. **Use reactive variables for UI state**: Ensures UI updates automatically
4. **Persist important state**: Use StorageService for data that needs to survive app restarts
5. **Use bindings for screen-specific controllers**: Ensures proper initialization and cleanup
