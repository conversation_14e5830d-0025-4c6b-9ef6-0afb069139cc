# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Removed
- Removed deprecated `social_login_controller.dart` file as the app now only supports mobile login
- Removed Google social SSO functionality which was already disabled
- Removed unused Google logo SVG asset
- Removed Google authentication related translations

### Changed
- Modified authentication flow to only show OTP verification for signup, not for login
- Updated translations to replace email references with mobile number references

### Added
- Added new `auth_utils.dart` utility class with authentication helper methods migrated from the removed social login controller:
  - `checkUserBlockedStatus()`: Checks if a user is blocked
  - `generateNonce()`: Generates a cryptographically secure random nonce
  - `sha256ofString()`: Returns the SHA-256 hash of an input string

### Changed
- Updated dependency injection documentation to reflect the removal of SocialLoginController
